2025-08-29 13:33:46.306 INFO  [main] c.a.n.c.e.SearchableProperties - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-29 13:33:46.363 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-29 13:33:49.224 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-29 13:33:49.224 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-29 13:33:52.923 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-29 13:33:52.932 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-29 13:33:52.936 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-29 13:33:52.938 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
2025-08-29 13:33:52.985 INFO  [main] c.h.AppMain - The following 1 profile is active: "druid"
2025-08-29 13:33:56.278 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-29 13:33:56.291 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-29 13:33:56.405 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 80 ms. Found 0 Elasticsearch repository interfaces.
2025-08-29 13:33:56.415 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-29 13:33:56.418 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-29 13:33:56.463 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 43 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-29 13:33:56.492 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-29 13:33:56.498 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-29 13:33:56.550 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-08-29 13:33:57.292 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:58.278 INFO  [main] o.s.c.c.s.GenericScope - BeanFactory id=345354b6-40df-36f1-977c-21a895912dba
2025-08-29 13:34:00.193 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:00.210 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:00.215 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$599/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:00.246 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:00.632 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:00.641 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:02.073 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 28183 (http)
2025-08-29 13:34:02.126 INFO  [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-28183"]
2025-08-29 13:34:02.133 INFO  [main] o.a.c.c.StandardService - Starting service [Tomcat]
2025-08-29 13:34:02.133 INFO  [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-29 13:34:02.595 INFO  [main] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-08-29 13:34:02.595 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9579 ms
2025-08-29 13:34:03.168 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
2025-08-29 13:34:04.229 INFO  [main] c.h.d.c.MybatisPlusEnhancerConfiguration - ✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
2025-08-29 13:34:08.945 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-29 13:34:08.945 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:08.964 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-29 13:34:08.965 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:08.983 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-29 13:34:08.983 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.002 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-29 13:34:09.002 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.017 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-29 13:34:09.018 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.036 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-29 13:34:09.036 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.054 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-29 13:34:09.054 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.071 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-29 13:34:09.071 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.089 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-29 13:34:09.089 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.106 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-29 13:34:09.106 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.123 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-29 13:34:09.124 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.143 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-29 13:34:09.143 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.163 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-29 13:34:09.199 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-29 13:34:09.199 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.224 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-29 13:34:09.225 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.240 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-29 13:34:09.240 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.257 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-29 13:34:09.257 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.278 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-29 13:34:09.278 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.297 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-29 13:34:09.297 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.318 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-29 13:34:09.319 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.336 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-29 13:34:09.337 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.354 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-29 13:34:09.354 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.369 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-29 13:34:09.369 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.385 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-29 13:34:09.385 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.437 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-29 13:34:09.437 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.463 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-29 13:34:09.463 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.478 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-29 13:34:09.478 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.492 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-29 13:34:09.493 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.510 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-29 13:34:09.511 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.525 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-29 13:34:09.526 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.541 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-29 13:34:09.542 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.556 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-29 13:34:09.557 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.571 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-29 13:34:09.572 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.588 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-29 13:34:09.588 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.603 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-29 13:34:09.603 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.618 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-29 13:34:09.619 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.634 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-29 13:34:09.634 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.650 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-29 13:34:09.650 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.680 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-29 13:34:09.680 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.702 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-29 13:34:09.702 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.720 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-29 13:34:09.720 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.740 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-29 13:34:09.740 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.759 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-29 13:34:09.759 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.777 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-29 13:34:09.777 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.795 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-29 13:34:09.795 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.810 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-29 13:34:09.811 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.827 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-29 13:34:09.828 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.844 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-29 13:34:09.844 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.873 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-29 13:34:09.873 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.905 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-29 13:34:09.905 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.935 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-29 13:34:09.935 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.953 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-29 13:34:09.954 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.971 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-29 13:34:09.971 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:10.558 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-task' URL not provided. Will try picking an instance via load-balancing.
2025-08-29 13:34:24.039 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:27.574 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:27.575 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:27.696 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:30.734 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:30.735 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:30.861 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:33.901 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:33.901 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:34.025 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:37.058 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:37.058 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:37.192 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:40.218 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:40.218 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:40.739 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:43.773 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:43.774 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:43.906 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:46.950 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:46.950 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:47.071 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:51.101 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:51.101 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:51.550 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
2025-08-29 13:34:51.551 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
2025-08-29 13:34:51.551 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
2025-08-29 13:34:51.551 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
2025-08-29 13:34:51.551 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
2025-08-29 13:34:51.551 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
2025-08-29 13:34:51.758 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:54.787 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:54.787 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:55.388 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:58.416 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:58.416 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:35:15.687 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-29 13:35:15.980 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-29 13:35:15.980 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-29 13:35:17.010 INFO  [main] c.h.n.c.SharedNacosNameSpace - {"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
2025-08-29 13:35:18.176 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-29 13:35:18.919 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
2025-08-29 13:35:24.010 INFO  [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-29 13:35:25.777 INFO  [main] c.h.s.c.SecurityConfig - [/test/dict, /dict/add-batch, /error/logs, /dict/api-query, /dict-test/test] --> 200
2025-08-29 13:35:26.217 INFO  [main] o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@154a3126, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@65111abe, org.springframework.security.web.context.SecurityContextPersistenceFilter@7b51d80d, org.springframework.security.web.header.HeaderWriterFilter@ca47d17, org.springframework.security.web.authentication.logout.LogoutFilter@2878caf6, org.springframework.web.filter.CorsFilter@2ad43051, com.hl.security.config.sso.SsoAuthTokenFilter@1a2bcce1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@64b3a31f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@371b39da, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6cb7fb13, org.springframework.security.web.session.SessionManagementFilter@291fd83d, org.springframework.security.web.access.ExceptionTranslationFilter@3f008d2d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@d0c86f7]
2025-08-29 13:35:34.519 INFO  [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-28183"]
2025-08-29 13:35:34.677 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 28183 (http) with context path ''
2025-08-29 13:35:35.825 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-29 13:35:35.833 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-29 13:35:35.833 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-29 13:35:36.099 INFO  [main] c.a.c.n.r.NacosServiceRegistry - nacos registry, default hl-wj-police-archive *************:28183 register finished
2025-08-29 13:35:39.842 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [**************:5672]
2025-08-29 13:35:40.252 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#27f3be72:0/SimpleConnection@2f9002da [delegate=amqp://admin@**************:5672/, localPort= 58963]
2025-08-29 13:35:40.860 INFO  [main] c.h.AppMain - Started AppMain in 116.589 seconds (JVM running for 126.775)
2025-08-29 13:35:40.863 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-29 13:35:40.863 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-29 13:35:45.373 INFO  [main] c.h.s.c.s.c.SsoCache - init cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
2025-08-29 13:35:45.378 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-29 13:35:45.379 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-29 13:35:45.689 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
2025-08-29 13:35:45.689 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
2025-08-29 13:35:45.690 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
2025-08-29 13:35:45.791 INFO  [main] c.h.d.s.i.DictDataServiceImpl - refresh dict cache 
2025-08-29 13:35:46.344 INFO  [RMI TCP Connection(12)-*************] o.a.c.c.C.[.[.[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-29 13:35:46.344 INFO  [RMI TCP Connection(12)-*************] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-29 13:35:46.412 INFO  [RMI TCP Connection(12)-*************] o.s.w.s.DispatcherServlet - Completed initialization in 68 ms
2025-08-29 13:35:47.383 WARN  [RMI TCP Connection(14)-*************] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor142.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-29 13:35:47.876 INFO  [main] c.h.l.c.l.LogSql - Execute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
2025-08-29 13:35:55.204 ERROR [RMI TCP Connection(14)-*************] c.a.d.f.s.StatFilter - slow sql 1838 millis. SELECT 1[]
2025-08-29 13:35:58.729 INFO  [RMI TCP Connection(14)-*************] c.a.d.p.DruidDataSource - {dataSource-1} inited
2025-08-29 13:35:58.850 WARN  [RMI TCP Connection(14)-*************] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 11242ms to respond
2025-08-29 13:36:03.136 INFO  [RMI TCP Connection(14)-*************] c.a.d.p.DruidDataSource - {dataSource-2} inited
2025-08-29 13:36:07.487 INFO  [RMI TCP Connection(14)-*************] c.a.d.p.DruidDataSource - {dataSource-3} inited
2025-08-29 13:36:12.891 INFO  [RMI TCP Connection(14)-*************] c.a.d.p.DruidDataSource - {dataSource-4} inited
2025-08-29 13:36:16.659 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.TaskEventListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"add\",\"data\":{\"a_cc_idcards\":\"/\",\"a_cc_keys\":\"SBCDEFGH\",\"a_helper_idcards\":\"/\",\"a_helper_keys\":\"@\",\"a_jkry_idcards\":\"/32011111111111111/32040219831018311X/32011419740827003X/320411198502282813/\",\"a_jkry_keys\":\"/SBCDEarchive.adminFGH0I/\",\"a_organization_fj\":\"************\",\"a_organization_pcs\":\"************\",\"a_organization_zrq\":\"\",\"a_work_idcards\":\"/******************/320401199210122812/32042119710723801X/\",\"a_work_keys\":\"488K34VPZF\",\"a_work_now_idcards\":\"/******************/320401199210122812/32042119710723801X/\",\"a_work_now_keys\":\"488K34VPZF\",\"circle_continue\":0,\"circle_num\":1,\"config_id\":\"TC1755148963645JZQ_WJ\",\"config_uuid\":\"CKSWWQQWW7H\",\"content\":{\"config_uuid\":\"CKSWWQQWW7H\",\"FormPersonName_OD_4Go00IyLi83UR\":\"M_hualong\",\"FormPersonOrg_z1kuVpHykRhP0XlT\":\"************\",\"FormPersonJob_lDGxBbl9KvR_RRPk\":\"民警\",\"FormCascaderSelect_rzAIylq9QPZWltOG\":[\"17514501902727715004\"],\"FormDate_9sizHeU70JIOd43k\":\"2025-09-01 00:00:00_2025-09-08 00:00:00\",\"FormInput_fxwe8TJlJRrDj_PJ\":\"11\",\"FormDate_d3Sbseq5u3RONs01\":\"2025-08-14\",\"FormInput_s_Cx1avzRqdZadeO\":\"111\",\"FormInput__GaGMzFBKIoavdx0\":\"11\",\"FormInput_CWtPqt1nhdaDzvkG\":\"11\",\"FormNumberSignature_dWM7EAcWY2Eha0EM\":\"/tyyh/preview/sign/hualong_1755049382627.png\",\"FormSelect_wS87jmOi9WVqkIwB\":\"股级干部\",\"sign_url\":\"M_hualong\",\"xm\":\"M_hualong\",\"dw\":\"************\",\"zw\":\"民警\",\"qwgj\":[\"17514501902727715004\"],\"tlsj\":\"2025-09-01 00:00:00_2025-09-08 00:00:00\",\"jfly\":\"11\",\"lqzjsj\":\"2025-08-14\",\"cgsy\":\"111\",\"yxxjqk\":\"11\",\"syxjqk\":\"11\",\"sqrqm\":\"/tyyh/preview/sign/hualong_1755049382627.png\",\"CKSWWQQWW7H_title\":\"M_hualong 因私出国(境)申请\"},\"create_organization\":\"************\",\"create_time\":\"2025-08-29 13:36:22\",\"error_type\":0,\"extend_str\":\"1756445782959OUQV1BV\",\"has_additional\":0,\"has_pz\":0,\"id_card\":\"hualong\",\"is_circle\":0,\"is_delete\":0,\"is_error\":0,\"last_time\":\"2025-08-29 13:36:22\",\"organization\":\"/************/\",\"person_info\":{\"6Hqnw_cY4Ryz0hJA\":{\"handler\":{\"488K34VPZF\":\"******************/320401199210122812/32042119710723801X\"},\"works\":\"******************/320401199210122812/32042119710723801X\"}},\"process_num\":1,\"status\":\"UNFINISH\",\"task_id\":\"1756445782959EAQNZ\",\"title\":\"M_hualong 因私出国(境)申请\",\"update_time\":\"2025-08-29 13:36:22\",\"work_starttime\":\"2025-08-29 13:36:22\",\"type\":\"add\",\"time\":\"2025-08-29 13:36:23\"}}","content_time":"2025-08-29 13:36:24","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"d4cbdb9c-0ad9-445b-aa46-86efa8a0af96"}
2025-08-29 13:36:17.240 INFO  [pool-20-thread-1] c.h.a.l.TaskEventListener - 使用策略处理任务: configUuid=CKSWWQQWW7H, opt=add, strategy=OverseasTravelStrategy
2025-08-29 13:36:17.242 INFO  [pool-20-thread-1] c.h.a.l.s.i.OverseasTravelStrategy - 处理因私出国任务，操作类型: add
2025-08-29 13:36:25.268 WARN  [RMI TCP Connection(14)-*************] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration$AdaptedReactiveHealthContributors$1 (redis) took 12318ms to respond
2025-08-29 13:38:34.087 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.TaskEventListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"work\",\"data\":{\"content\":{\"FormDate_XdyoIf3Dx2SWRmJe\":\"2025-08-07\",\"id\":\"\",\"zjffsj\":\"2025-08-07\",\"id_card\":\"32040219831018311X\"},\"id_card\":\"32040219831018311X\",\"node_id\":\"6oVFCldrTkMjA0ed\",\"result_id\":\"\",\"task_id\":\"175523474316761LGC\",\"config_uuid\":\"CKSWWQQWW7H\",\"type\":\"work\",\"time\":\"2025-08-29 13:38:40\",\"form_field_list\":[{\"_style\":{},\"_property\":{\"label\":\"证件发放时间\",\"label_width\":\"\",\"label_id\":\"zjffsj\",\"guide\":\"\",\"date_type\":\"date\",\"required\":true,\"date_format\":\"YYYY-MM-DD\",\"is_range\":false,\"editable\":true,\"show\":[],\"status\":1,\"is_sort\":0,\"is_two_column\":1,\"default_value\":{\"type\":\"datetime\"},\"is_default_search\":0},\"id\":\"FormDate_XdyoIf3Dx2SWRmJe\",\"key\":\"FormDate\"}],\"custom_id\":\"6oVFCldrTkMjA0ed\"}}","content_time":"2025-08-29 13:38:41","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"19644244-7402-428c-b3d0-c90ca9f54dfd"}
2025-08-29 13:38:34.093 INFO  [pool-20-thread-2] c.h.a.l.TaskEventListener - 使用策略处理任务: configUuid=CKSWWQQWW7H, opt=work, strategy=OverseasTravelStrategy
2025-08-29 13:38:34.093 INFO  [pool-20-thread-2] c.h.a.l.s.i.OverseasTravelStrategy - 处理因私出国任务，操作类型: work
2025-08-29 13:40:56.415 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.TaskEventListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"audit\",\"data\":{\"content\":{\"pass\":1,\"text\":\"11\",\"sign_url\":\"/tyyh/preview/sign/32011419740827003X_1755133592424.png\",\"id_card\":\"32011419740827003X\"},\"node_id\":\"jIeYxcy6jYMhPQm5\",\"result_id\":\"\",\"task_id\":\"1756445782959EAQNZ\",\"id_card\":\"32011419740827003X\",\"config_uuid\":\"CKSWWQQWW7H\",\"type\":\"audit\",\"time\":\"2025-08-29 13:41:02\",\"form_field_list\":[{\"_style\":{},\"_property\":{\"editable\":true,\"label\":\"是否通过\",\"label_width\":\"\",\"label_id\":\"\",\"placeholder\":\"\",\"expand\":true,\"guide\":\"\",\"format\":\"\",\"required\":true,\"readonly\":true,\"is_digital_signature\":1,\"default_value\":{\"type\":\"custome\",\"value\":\"\"},\"options\":[{\"label\":\"通过\",\"value\":1},{\"label\":\"驳回\",\"value\":0}],\"status\":1,\"is_use_seal\":0,\"is_use_name\":1,\"no_sign_use_police\":0},\"id\":\"pass\",\"key\":\"FormSelect\",\"notSubmit\":false},{\"_style\":{},\"_property\":{\"editable\":true,\"label\":\"审批说明\",\"label_width\":\"\",\"label_id\":\"\",\"placeholder\":\"\",\"guide\":\"\",\"format\":\"\",\"required\":false,\"readonly\":true,\"default_value\":{\"type\":\"custome\",\"value\":\"\"},\"status\":1},\"id\":\"text\",\"key\":\"FormTextarea\",\"notSubmit\":false}],\"custom_id\":\"ZWSP\"}}","content_time":"2025-08-29 13:41:04","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"77309488-f100-4d12-a9db-3db3800b0ec5"}
2025-08-29 13:40:56.416 INFO  [pool-20-thread-3] c.h.a.l.TaskEventListener - 使用策略处理任务: configUuid=CKSWWQQWW7H, opt=audit, strategy=OverseasTravelStrategy
2025-08-29 13:40:56.416 INFO  [pool-20-thread-3] c.h.a.l.s.i.OverseasTravelStrategy - 处理因私出国任务，操作类型: audit
2025-08-29 13:44:25.556 INFO  [pool-20-thread-3] c.h.l.c.l.LogSql - Execute SQL：INSERT INTO police_overseas_travel ( id_card, destination_country, start_date, end_date, travel_reason, data_type, task_id ) VALUES ( 'hualong', '蒙古', '2025-09-01T00:00', '2025-09-08T00:00', '111', 1, '1756445782959EAQNZ' )
2025-08-29 13:45:42.488 WARN  [Thread-32] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-29 13:45:42.488 WARN  [Thread-32] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-29 13:45:42.488 WARN  [Thread-26] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-29 13:45:42.495 WARN  [Thread-26] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-29 13:45:42.538 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-08-29 13:45:42.541 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-29 13:45:42.625 INFO  [tomcat-shutdown] o.s.b.w.e.t.GracefulShutdown - Graceful shutdown complete
2025-08-29 13:45:43.352 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-08-29 13:45:43.407 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-29 13:45:43.411 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-29 13:45:43.740 INFO  [SpringApplicationShutdownHook] c.h.c.t.GraceThreadPool - ==== 优雅关闭线程池 ====
2025-08-29 13:45:43.753 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closing ...
2025-08-29 13:45:43.784 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closed
2025-08-29 13:45:43.784 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closing ...
2025-08-29 13:45:43.792 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closed
2025-08-29 13:45:43.792 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closing ...
2025-08-29 13:45:43.814 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closed
2025-08-29 13:45:43.815 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closing ...
2025-08-29 13:45:43.822 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closed
2025-08-30 14:51:02.383 INFO  [main] c.a.n.c.e.SearchableProperties - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-30 14:51:02.444 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-30 14:51:05.285 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 14:51:05.285 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 14:51:08.874 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-30 14:51:08.884 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-30 14:51:08.889 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-30 14:51:08.891 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
2025-08-30 14:51:08.937 INFO  [main] c.h.AppMain - The following 1 profile is active: "druid"
2025-08-30 14:51:12.246 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 14:51:12.258 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-30 14:51:12.358 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 69 ms. Found 0 Elasticsearch repository interfaces.
2025-08-30 14:51:12.366 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 14:51:12.368 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-30 14:51:12.418 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 49 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-30 14:51:12.443 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 14:51:12.447 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-30 14:51:12.503 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-08-30 14:51:13.184 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.184 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.184 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:14.166 INFO  [main] o.s.c.c.s.GenericScope - BeanFactory id=85576418-724e-3b81-b22a-c198497d133c
2025-08-30 14:51:15.843 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:51:15.854 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:51:15.857 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$599/736640638] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:51:15.873 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:51:16.179 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:51:16.187 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:51:17.537 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 28183 (http)
2025-08-30 14:51:17.590 INFO  [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-28183"]
2025-08-30 14:51:17.597 INFO  [main] o.a.c.c.StandardService - Starting service [Tomcat]
2025-08-30 14:51:17.597 INFO  [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-30 14:51:18.055 INFO  [main] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 14:51:18.055 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9088 ms
2025-08-30 14:51:18.607 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 14:51:19.768 INFO  [main] c.h.d.c.MybatisPlusEnhancerConfiguration - ✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
2025-08-30 14:51:23.853 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-30 14:51:23.854 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.872 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-30 14:51:23.872 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.890 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-30 14:51:23.890 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.907 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-30 14:51:23.907 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.923 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-30 14:51:23.923 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.940 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-30 14:51:23.940 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.957 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-30 14:51:23.957 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.972 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-30 14:51:23.973 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.991 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-30 14:51:23.991 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.008 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-30 14:51:24.008 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.026 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-30 14:51:24.026 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.044 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-30 14:51:24.044 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.062 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-30 14:51:24.098 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-30 14:51:24.098 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.122 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-30 14:51:24.122 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.141 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-30 14:51:24.141 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.162 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-30 14:51:24.162 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.181 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-30 14:51:24.181 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.200 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-30 14:51:24.200 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.222 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-30 14:51:24.222 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.241 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-30 14:51:24.241 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.257 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-30 14:51:24.257 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.272 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-30 14:51:24.272 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.288 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-30 14:51:24.289 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.326 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-30 14:51:24.326 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.352 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-30 14:51:24.352 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.368 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-30 14:51:24.369 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.385 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-30 14:51:24.386 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.403 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-30 14:51:24.403 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.419 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-30 14:51:24.419 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.436 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-30 14:51:24.436 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.451 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-30 14:51:24.451 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.465 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-30 14:51:24.465 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.480 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-30 14:51:24.480 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.498 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-30 14:51:24.499 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.517 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-30 14:51:24.517 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.532 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-30 14:51:24.533 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.546 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-30 14:51:24.548 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.580 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-30 14:51:24.580 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.605 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-30 14:51:24.606 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.622 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-30 14:51:24.623 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.643 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-30 14:51:24.643 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.661 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-30 14:51:24.662 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.695 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-30 14:51:24.696 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.713 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-30 14:51:24.713 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.730 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-30 14:51:24.730 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.746 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-30 14:51:24.746 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.763 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-30 14:51:24.763 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.793 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-30 14:51:24.793 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.824 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-30 14:51:24.824 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.853 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-30 14:51:24.853 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.872 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-30 14:51:24.872 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.890 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-30 14:51:24.891 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:25.431 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-task' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 14:51:40.080 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 14:51:43.603 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:51:43.604 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:51:43.707 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 14:51:46.767 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:51:46.768 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:51:46.898 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 14:51:49.935 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:51:49.935 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:51:50.064 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 14:51:55.097 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:51:55.097 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:51:55.231 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 14:51:58.271 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:51:58.271 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:51:58.401 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 14:52:01.422 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:52:01.423 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:52:01.552 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 14:52:04.592 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:52:04.592 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:52:04.721 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 14:52:07.757 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:52:07.757 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:52:08.208 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
2025-08-30 14:52:08.208 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
2025-08-30 14:52:08.208 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
2025-08-30 14:52:08.208 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
2025-08-30 14:52:08.208 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
2025-08-30 14:52:08.208 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
2025-08-30 14:52:08.413 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 14:52:11.440 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:52:11.440 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:52:11.736 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 14:52:14.769 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:52:14.769 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:52:31.658 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 14:52:31.884 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 14:52:31.884 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 14:52:32.895 INFO  [main] c.h.n.c.SharedNacosNameSpace - {"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
2025-08-30 14:52:34.023 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 14:52:34.795 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 14:52:39.857 INFO  [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-30 14:52:41.449 INFO  [main] c.h.s.c.SecurityConfig - [/dict/add-batch, /test/dict, /dict/api-query, /dict-test/test, /error/logs] --> 200
2025-08-30 14:52:41.819 INFO  [main] o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@335d3b03, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@510bd513, org.springframework.security.web.context.SecurityContextPersistenceFilter@74a218dc, org.springframework.security.web.header.HeaderWriterFilter@21118872, org.springframework.security.web.authentication.logout.LogoutFilter@5699f268, org.springframework.web.filter.CorsFilter@3ae77255, com.hl.security.config.sso.SsoAuthTokenFilter@5c44bce3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@79bbdd58, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@634826ed, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c5fda87, org.springframework.security.web.session.SessionManagementFilter@606a1086, org.springframework.security.web.access.ExceptionTranslationFilter@25a98a27, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@343bc431]
2025-08-30 14:52:49.445 INFO  [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-28183"]
2025-08-30 14:52:49.601 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 28183 (http) with context path ''
2025-08-30 14:52:50.706 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 14:52:50.712 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 14:52:50.713 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 14:52:50.981 INFO  [main] c.a.c.n.r.NacosServiceRegistry - nacos registry, default hl-wj-police-archive *************:28183 register finished
2025-08-30 14:52:54.761 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [**************:5672]
2025-08-30 14:52:55.162 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#4c5b5a1c:0/SimpleConnection@351108fa [delegate=amqp://admin@**************:5672/, localPort= 56353]
2025-08-30 14:52:55.782 INFO  [main] c.h.AppMain - Started AppMain in 115.424 seconds (JVM running for 121.454)
2025-08-30 14:52:55.784 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 14:52:55.784 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 14:52:59.774 INFO  [main] c.h.s.c.s.c.SsoCache - init cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 2025-08-30 10:41:26
2025-08-30 14:52:59.780 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 14:52:59.780 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 14:53:00.045 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
2025-08-30 14:53:00.046 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
2025-08-30 14:53:00.047 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
2025-08-30 14:53:00.113 INFO  [main] c.h.d.s.i.DictDataServiceImpl - refresh dict cache 
2025-08-30 14:53:00.881 INFO  [RMI TCP Connection(6)-*************] o.a.c.c.C.[.[.[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 14:53:00.881 INFO  [RMI TCP Connection(6)-*************] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 14:53:00.948 INFO  [RMI TCP Connection(6)-*************] o.s.w.s.DispatcherServlet - Completed initialization in 67 ms
2025-08-30 14:53:01.693 INFO  [main] c.h.l.c.l.LogSql - Execute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
2025-08-30 14:53:01.881 WARN  [RMI TCP Connection(7)-*************] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor142.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-30 14:53:08.689 ERROR [main] c.a.d.f.s.StatFilter - slow sql 1857 millis. SELECT 1[]
2025-08-30 14:53:12.211 INFO  [main] c.a.d.p.DruidDataSource - {dataSource-1} inited
2025-08-30 14:53:12.319 WARN  [RMI TCP Connection(7)-*************] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 10302ms to respond
2025-08-30 14:53:16.627 INFO  [RMI TCP Connection(7)-*************] c.a.d.p.DruidDataSource - {dataSource-2} inited
2025-08-30 14:53:20.955 INFO  [RMI TCP Connection(7)-*************] c.a.d.p.DruidDataSource - {dataSource-3} inited
2025-08-30 14:53:26.437 INFO  [RMI TCP Connection(7)-*************] c.a.d.p.DruidDataSource - {dataSource-4} inited
2025-08-30 14:56:12.985 INFO  [http-nio-28183-exec-1] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {} -> 134ms -> {"stackTrace":[{"fileName":"PoliceTagInfoService.java","lineNumber":246,"className":"com.hl.archive.service.PoliceTagInfoService","methodName":"pagePolicePersonalTag"},{"fileName":"<generated>","lineNumber":-1,"className":"com.hl.archive.service.PoliceTagInfoService$$FastClassBySpringCGLIB$$1","methodName":"invoke"},{"fileName":"MethodProxy.java","lineNumber":218,"className":"org.springframework.cglib.proxy.MethodProxy","methodName":"invoke"},{"fileName":"CglibAopProxy.java","lineNumber":386,"className":"org.springframework.aop.framework.CglibAopProxy","methodName":"invokeMethod"},{"fileName":"CglibAopProxy.java","lineNumber":85,"className":"org.springframework.aop.framework.CglibAopProxy","methodName":"access$000"},{"fileName":"CglibAopProxy.java","lineNumber":703,"className":"org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor","methodName":"intercept"},{"fileName":"<generated>","lineNumber":-1,"className":"com.hl.archive.service.PoliceTagInfoService$$EnhancerBy
2025-08-30 14:56:12.994 ERROR [http-nio-28183-exec-1] c.h.c.c.e.GlobalExceptionHandler - /policeTagInfo/queryPersonTag --> 
java.lang.NullPointerException: null
	at com.hl.archive.service.PoliceTagInfoService.pagePolicePersonalTag(PoliceTagInfoService.java:246) ~[classes/:?]
	at com.hl.archive.service.PoliceTagInfoService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceTagInfoService$$EnhancerBySpringCGLIB$$1.pagePolicePersonalTag(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController.queryPersonTag(PoliceTagInfoController.java:110) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceTagInfoController$$EnhancerBySpringCGLIB$$1.queryPersonTag(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
2025-08-30 14:57:16.975 INFO  [http-nio-28183-exec-7] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 14:57:18.784 INFO  [http-nio-28183-exec-7] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 1798ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":"[\"星火计划\"]","tagTypeList":"[\"xinghuo_plan\"]"},{"idCard":"320223197803056617","tagNameList":"[\"星火计划\"]","tagTypeList":"[\"xinghuo_plan\"]"},{"idCard":"320401198103162811","tagNameList":"[\"专家型\"]","tagTypeList":"[\"combat_ability\"]"},{"idCard":"320401199210122812","tagNameList":"[\"优秀学员（提名）\"]","tagTypeList":"[\"dengfeng_training\"]"},{"idCard":"320402198603183412","tagNameList":"[\"星火计划\"]","tagTypeList":"[\"xinghuo_plan\"]"},{"idCard":"320411198210083417","tagNameList":"[\"星火计划\"]","tagTypeList":"[\"xinghuo_plan\"]"},{"idCard":"320411198811147519","tagNameList":"[\"星火计划\"]","tagTypeList":"[\"xinghuo_plan\"]"},{"idCard":"320421197411208114","tagNameList":"[\"星火计划\"]","tagTypeList":"[\"xinghuo_plan\"]"},{"idCard":"320421197612131723","tagNameList":"[\"星火计划\"]","tagTypeList":"[\"xinghuo_plan\"]"},{"idCard":"320421197704083618","tagNameList":"[\"星火计划\"]","tagTypeList":"[\"xinghuo_plan\"]"}],"errno":200,"error":"操作成功"}
2025-08-30 14:58:35.613 INFO  [http-nio-28183-exec-15] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-30 14:58:35.621 INFO  [http-nio-28183-exec-15] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-08-30 14:58:35.623 WARN  [http-nio-28183-exec-15] o.m.s.m.ClassPathMapperScanner - No MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
2025-08-30 14:58:35.636 INFO  [http-nio-28183-exec-15] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-30 14:58:35.643 INFO  [http-nio-28183-exec-15] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Elasticsearch repository interfaces.
2025-08-30 14:58:35.645 INFO  [http-nio-28183-exec-15] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-30 14:58:35.649 INFO  [http-nio-28183-exec-15] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-30 14:58:35.788 INFO  [http-nio-28183-exec-15] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 14:58:35.857 INFO  [http-nio-28183-exec-15] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 58ms -> {"cause":{"cause":{"localizedMessage":"argument type mismatch","message":"argument type mismatch","stackTrace":[{"fileName":"NativeMethodAccessorImpl.java","lineNumber":-2,"className":"sun.reflect.NativeMethodAccessorImpl","methodName":"invoke0"},{"fileName":"NativeMethodAccessorImpl.java","lineNumber":62,"className":"sun.reflect.NativeMethodAccessorImpl","methodName":"invoke"},{"fileName":"DelegatingMethodAccessorImpl.java","lineNumber":43,"className":"sun.reflect.DelegatingMethodAccessorImpl","methodName":"invoke"},{"fileName":"Method.java","lineNumber":498,"className":"java.lang.reflect.Method","methodName":"invoke"},{"fileName":"MethodInvoker.java","lineNumber":44,"className":"org.apache.ibatis.reflection.invoker.MethodInvoker","methodName":"invoke"},{"fileName":"BeanWrapper.java","lineNumber":172,"className":"org.apache.ibatis.reflection.wrapper.BeanWrapper","methodName":"setBeanProperty"},{"fileName":"BeanWrapper.java","lineNumber":58,"className":"org.apache.ibatis.reflection.wra
2025-08-30 14:58:35.859 ERROR [http-nio-28183-exec-15] c.h.c.c.e.GlobalExceptionHandler - /policeTagInfo/queryPersonTag --> 
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy199.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:119) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:84) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.sun.proxy.$Proxy263.pagePolicePersonalTag(Unknown Source) ~[?:?]
	at com.hl.archive.service.PoliceTagInfoService.pagePolicePersonalTag(PoliceTagInfoService.java:246) ~[classes/:?]
	at com.hl.archive.service.PoliceTagInfoService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceTagInfoService$$EnhancerBySpringCGLIB$$1.pagePolicePersonalTag(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController.queryPersonTag(PoliceTagInfoController.java:110) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceTagInfoController$$EnhancerBySpringCGLIB$$1.queryPersonTag(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:178) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.reflection.invoker.MethodInvoker.invoke(MethodInvoker.java:44) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:172) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
2025-08-30 14:58:58.053 INFO  [http-nio-28183-exec-4] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 14:58:58.072 INFO  [http-nio-28183-exec-4] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 16ms -> {"cause":{"cause":{"localizedMessage":"argument type mismatch","message":"argument type mismatch","stackTrace":[{"fileName":"NativeMethodAccessorImpl.java","lineNumber":-2,"className":"sun.reflect.NativeMethodAccessorImpl","methodName":"invoke0"},{"fileName":"NativeMethodAccessorImpl.java","lineNumber":62,"className":"sun.reflect.NativeMethodAccessorImpl","methodName":"invoke"},{"fileName":"DelegatingMethodAccessorImpl.java","lineNumber":43,"className":"sun.reflect.DelegatingMethodAccessorImpl","methodName":"invoke"},{"fileName":"Method.java","lineNumber":498,"className":"java.lang.reflect.Method","methodName":"invoke"},{"fileName":"MethodInvoker.java","lineNumber":44,"className":"org.apache.ibatis.reflection.invoker.MethodInvoker","methodName":"invoke"},{"fileName":"BeanWrapper.java","lineNumber":172,"className":"org.apache.ibatis.reflection.wrapper.BeanWrapper","methodName":"setBeanProperty"},{"fileName":"BeanWrapper.java","lineNumber":58,"className":"org.apache.ibatis.reflection.wra
2025-08-30 14:58:58.073 ERROR [http-nio-28183-exec-4] c.h.c.c.e.GlobalExceptionHandler - /policeTagInfo/queryPersonTag --> 
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy199.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:119) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:84) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.sun.proxy.$Proxy263.pagePolicePersonalTag(Unknown Source) ~[?:?]
	at com.hl.archive.service.PoliceTagInfoService.pagePolicePersonalTag(PoliceTagInfoService.java:246) ~[classes/:?]
	at com.hl.archive.service.PoliceTagInfoService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceTagInfoService$$EnhancerBySpringCGLIB$$1.pagePolicePersonalTag(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController.queryPersonTag(PoliceTagInfoController.java:110) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceTagInfoController$$EnhancerBySpringCGLIB$$1.queryPersonTag(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:178) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.reflection.invoker.MethodInvoker.invoke(MethodInvoker.java:44) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:172) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
2025-08-30 14:58:58.631 INFO  [http-nio-28183-exec-21] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 14:58:58.653 INFO  [http-nio-28183-exec-21] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 20ms -> {"cause":{"cause":{"localizedMessage":"argument type mismatch","message":"argument type mismatch","stackTrace":[{"fileName":"NativeMethodAccessorImpl.java","lineNumber":-2,"className":"sun.reflect.NativeMethodAccessorImpl","methodName":"invoke0"},{"fileName":"NativeMethodAccessorImpl.java","lineNumber":62,"className":"sun.reflect.NativeMethodAccessorImpl","methodName":"invoke"},{"fileName":"DelegatingMethodAccessorImpl.java","lineNumber":43,"className":"sun.reflect.DelegatingMethodAccessorImpl","methodName":"invoke"},{"fileName":"Method.java","lineNumber":498,"className":"java.lang.reflect.Method","methodName":"invoke"},{"fileName":"MethodInvoker.java","lineNumber":44,"className":"org.apache.ibatis.reflection.invoker.MethodInvoker","methodName":"invoke"},{"fileName":"BeanWrapper.java","lineNumber":172,"className":"org.apache.ibatis.reflection.wrapper.BeanWrapper","methodName":"setBeanProperty"},{"fileName":"BeanWrapper.java","lineNumber":58,"className":"org.apache.ibatis.reflection.wra
2025-08-30 14:58:58.653 ERROR [http-nio-28183-exec-21] c.h.c.c.e.GlobalExceptionHandler - /policeTagInfo/queryPersonTag --> 
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy199.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:119) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:84) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.sun.proxy.$Proxy263.pagePolicePersonalTag(Unknown Source) ~[?:?]
	at com.hl.archive.service.PoliceTagInfoService.pagePolicePersonalTag(PoliceTagInfoService.java:246) ~[classes/:?]
	at com.hl.archive.service.PoliceTagInfoService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceTagInfoService$$EnhancerBySpringCGLIB$$1.pagePolicePersonalTag(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController.queryPersonTag(PoliceTagInfoController.java:110) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceTagInfoController$$EnhancerBySpringCGLIB$$1.queryPersonTag(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:178) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.reflection.invoker.MethodInvoker.invoke(MethodInvoker.java:44) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:172) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
2025-08-30 14:59:31.057 WARN  [Thread-32] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 14:59:31.057 WARN  [Thread-26] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 14:59:31.058 WARN  [Thread-32] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 14:59:31.061 WARN  [Thread-26] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 14:59:31.164 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-08-30 14:59:31.167 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-30 14:59:31.228 INFO  [tomcat-shutdown] o.s.b.w.e.t.GracefulShutdown - Graceful shutdown complete
2025-08-30 14:59:31.770 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-08-30 14:59:31.835 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 14:59:31.843 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 14:59:32.239 INFO  [SpringApplicationShutdownHook] c.h.c.t.GraceThreadPool - ==== 优雅关闭线程池 ====
2025-08-30 14:59:32.263 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closing ...
2025-08-30 14:59:32.296 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closed
2025-08-30 14:59:32.296 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closing ...
2025-08-30 14:59:32.304 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closed
2025-08-30 14:59:32.304 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closing ...
2025-08-30 14:59:32.311 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closed
2025-08-30 14:59:32.312 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closing ...
2025-08-30 14:59:32.319 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closed
2025-08-30 14:59:40.723 INFO  [main] c.a.n.c.e.SearchableProperties - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-30 14:59:40.780 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-30 14:59:43.456 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 14:59:43.456 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 14:59:46.766 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-30 14:59:46.776 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-30 14:59:46.780 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-30 14:59:46.783 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
2025-08-30 14:59:46.829 INFO  [main] c.h.AppMain - The following 1 profile is active: "druid"
2025-08-30 14:59:49.676 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 14:59:49.687 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-30 14:59:49.785 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 70 ms. Found 0 Elasticsearch repository interfaces.
2025-08-30 14:59:49.793 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 14:59:49.795 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-30 14:59:49.832 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-30 14:59:49.852 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 14:59:49.856 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-30 14:59:49.902 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-08-30 14:59:50.518 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:51.477 INFO  [main] o.s.c.c.s.GenericScope - BeanFactory id=85576418-724e-3b81-b22a-c198497d133c
2025-08-30 14:59:53.019 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:59:53.031 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:59:53.034 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$599/186540981] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:59:53.051 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:59:53.339 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:59:53.346 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 14:59:54.653 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 28183 (http)
2025-08-30 14:59:54.704 INFO  [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-28183"]
2025-08-30 14:59:54.710 INFO  [main] o.a.c.c.StandardService - Starting service [Tomcat]
2025-08-30 14:59:54.711 INFO  [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-30 14:59:55.127 INFO  [main] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 14:59:55.128 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8270 ms
2025-08-30 14:59:55.666 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 14:59:56.817 INFO  [main] c.h.d.c.MybatisPlusEnhancerConfiguration - ✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
2025-08-30 15:00:00.798 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-30 15:00:00.799 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.816 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-30 15:00:00.816 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.832 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-30 15:00:00.833 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.849 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-30 15:00:00.849 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.865 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-30 15:00:00.865 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.882 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-30 15:00:00.882 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.899 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-30 15:00:00.899 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.915 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-30 15:00:00.916 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.933 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-30 15:00:00.933 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.950 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-30 15:00:00.950 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.968 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-30 15:00:00.968 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.988 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-30 15:00:00.988 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.005 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-30 15:00:01.040 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-30 15:00:01.040 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.062 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-30 15:00:01.062 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.079 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-30 15:00:01.080 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.096 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-30 15:00:01.098 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.117 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-30 15:00:01.117 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.135 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-30 15:00:01.135 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.156 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-30 15:00:01.156 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.174 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-30 15:00:01.174 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.189 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-30 15:00:01.190 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.204 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-30 15:00:01.204 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.219 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-30 15:00:01.219 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.254 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-30 15:00:01.254 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.279 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-30 15:00:01.280 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.297 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-30 15:00:01.297 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.312 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-30 15:00:01.312 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.329 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-30 15:00:01.329 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.356 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-30 15:00:01.356 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.373 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-30 15:00:01.374 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.389 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-30 15:00:01.389 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.403 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-30 15:00:01.403 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.418 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-30 15:00:01.418 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.432 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-30 15:00:01.432 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.448 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-30 15:00:01.449 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.464 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-30 15:00:01.464 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.478 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-30 15:00:01.479 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.507 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-30 15:00:01.507 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.529 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-30 15:00:01.530 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.545 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-30 15:00:01.545 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.563 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-30 15:00:01.563 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.583 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-30 15:00:01.583 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.600 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-30 15:00:01.600 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.616 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-30 15:00:01.616 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.632 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-30 15:00:01.633 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.649 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-30 15:00:01.649 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.666 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-30 15:00:01.666 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.695 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-30 15:00:01.695 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.725 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-30 15:00:01.725 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.754 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-30 15:00:01.755 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.773 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-30 15:00:01.773 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.789 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-30 15:00:01.789 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:02.346 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-task' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 15:00:15.995 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:00:20.504 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:20.505 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:20.626 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:00:23.670 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:23.670 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:23.830 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:00:26.863 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:26.863 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:26.987 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:00:30.024 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:30.024 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:30.155 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:00:33.184 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:33.184 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:33.303 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:00:37.337 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:37.337 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:37.464 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:00:40.498 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:40.498 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:40.612 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:00:43.648 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:43.648 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:44.072 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
2025-08-30 15:00:44.072 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
2025-08-30 15:00:44.072 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
2025-08-30 15:00:44.072 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
2025-08-30 15:00:44.072 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
2025-08-30 15:00:44.072 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
2025-08-30 15:00:44.271 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:00:47.299 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:47.300 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:47.582 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:00:50.613 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:50.613 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:01:06.753 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 15:01:06.988 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:01:06.988 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:01:07.949 INFO  [main] c.h.n.c.SharedNacosNameSpace - {"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
2025-08-30 15:01:09.075 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 15:01:09.821 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 15:01:14.702 INFO  [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-30 15:01:16.234 INFO  [main] c.h.s.c.SecurityConfig - [/dict-test/test, /dict/add-batch, /dict/api-query, /error/logs, /test/dict] --> 200
2025-08-30 15:01:16.593 INFO  [main] o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3492979d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@72edd37c, org.springframework.security.web.context.SecurityContextPersistenceFilter@3a75f9d1, org.springframework.security.web.header.HeaderWriterFilter@7a7a07c2, org.springframework.security.web.authentication.logout.LogoutFilter@19b15284, org.springframework.web.filter.CorsFilter@6b7471b4, com.hl.security.config.sso.SsoAuthTokenFilter@2be49c8c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6c9725c6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4320387e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3ed2295, org.springframework.security.web.session.SessionManagementFilter@7f54e9f3, org.springframework.security.web.access.ExceptionTranslationFilter@3578546a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2fd4226e]
2025-08-30 15:01:23.926 INFO  [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-28183"]
2025-08-30 15:01:24.078 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 28183 (http) with context path ''
2025-08-30 15:01:25.179 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 15:01:25.189 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:01:25.189 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:01:25.450 INFO  [main] c.a.c.n.r.NacosServiceRegistry - nacos registry, default hl-wj-police-archive *************:28183 register finished
2025-08-30 15:01:29.208 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [**************:5672]
2025-08-30 15:01:29.611 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#43246511:0/SimpleConnection@a7f78eb [delegate=amqp://admin@**************:5672/, localPort= 63376]
2025-08-30 15:01:30.232 INFO  [main] c.h.AppMain - Started AppMain in 111.363 seconds (JVM running for 116.538)
2025-08-30 15:01:30.234 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:01:30.235 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:01:34.007 INFO  [main] c.h.s.c.s.c.SsoCache - init cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
2025-08-30 15:01:34.012 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:01:34.012 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:01:34.299 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
2025-08-30 15:01:34.299 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
2025-08-30 15:01:34.299 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
2025-08-30 15:01:34.366 INFO  [main] c.h.d.s.i.DictDataServiceImpl - refresh dict cache 
2025-08-30 15:01:35.011 INFO  [RMI TCP Connection(5)-*************] o.a.c.c.C.[.[.[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 15:01:35.011 INFO  [RMI TCP Connection(5)-*************] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 15:01:35.048 INFO  [RMI TCP Connection(5)-*************] o.s.w.s.DispatcherServlet - Completed initialization in 37 ms
2025-08-30 15:01:35.777 INFO  [main] c.h.l.c.l.LogSql - Execute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
2025-08-30 15:01:36.020 WARN  [RMI TCP Connection(7)-*************] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor141.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-30 15:01:42.927 ERROR [main] c.a.d.f.s.StatFilter - slow sql 2024 millis. SELECT 1[]
2025-08-30 15:01:46.804 INFO  [main] c.a.d.p.DruidDataSource - {dataSource-1} inited
2025-08-30 15:01:47.046 WARN  [RMI TCP Connection(7)-*************] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 10905ms to respond
2025-08-30 15:01:47.512 INFO  [http-nio-28183-exec-1] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:01:49.520 INFO  [http-nio-28183-exec-1] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 2153ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:01:51.429 INFO  [RMI TCP Connection(7)-*************] c.a.d.p.DruidDataSource - {dataSource-2} inited
2025-08-30 15:01:55.767 INFO  [RMI TCP Connection(7)-*************] c.a.d.p.DruidDataSource - {dataSource-3} inited
2025-08-30 15:02:01.174 INFO  [RMI TCP Connection(7)-*************] c.a.d.p.DruidDataSource - {dataSource-4} inited
2025-08-30 15:02:01.547 INFO  [http-nio-28183-exec-16] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:01.713 INFO  [http-nio-28183-exec-16] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 16ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:02.713 INFO  [http-nio-28183-exec-26] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:02.761 INFO  [http-nio-28183-exec-26] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 49ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:03.766 INFO  [http-nio-28183-exec-29] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:03.781 INFO  [http-nio-28183-exec-29] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 16ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:04.419 INFO  [http-nio-28183-exec-14] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:04.436 INFO  [http-nio-28183-exec-14] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 19ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:04.999 INFO  [http-nio-28183-exec-30] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:05.013 INFO  [http-nio-28183-exec-30] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 14ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:05.502 INFO  [http-nio-28183-exec-31] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:05.562 INFO  [http-nio-28183-exec-31] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 61ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:06.451 INFO  [http-nio-28183-exec-32] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:06.472 INFO  [http-nio-28183-exec-32] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 29ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:21.420 INFO  [http-nio-28183-exec-2] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:21.435 INFO  [http-nio-28183-exec-2] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 15ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:47.813 INFO  [http-nio-28183-exec-11] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:47.825 INFO  [http-nio-28183-exec-11] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 12ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:49.462 INFO  [http-nio-28183-exec-19] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:49.475 INFO  [http-nio-28183-exec-19] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 13ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:50.049 INFO  [http-nio-28183-exec-10] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:50.062 INFO  [http-nio-28183-exec-10] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 13ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:50.400 INFO  [http-nio-28183-exec-36] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:50.411 INFO  [http-nio-28183-exec-36] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 12ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:50.744 INFO  [http-nio-28183-exec-27] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:50.756 INFO  [http-nio-28183-exec-27] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 12ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:50.968 INFO  [http-nio-28183-exec-25] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:50.982 INFO  [http-nio-28183-exec-25] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 15ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:51.208 INFO  [http-nio-28183-exec-33] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:51.221 INFO  [http-nio-28183-exec-33] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 12ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:51.397 INFO  [http-nio-28183-exec-21] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:51.409 INFO  [http-nio-28183-exec-21] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 12ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:51.592 INFO  [http-nio-28183-exec-18] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:51.608 INFO  [http-nio-28183-exec-18] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 17ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:51.800 INFO  [http-nio-28183-exec-17] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:51.814 INFO  [http-nio-28183-exec-17] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 12ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:51.991 INFO  [http-nio-28183-exec-28] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:52.005 INFO  [http-nio-28183-exec-28] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 14ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:52.208 INFO  [http-nio-28183-exec-24] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:52.221 INFO  [http-nio-28183-exec-24] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 13ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:52.406 INFO  [http-nio-28183-exec-34] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:52.421 INFO  [http-nio-28183-exec-34] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 15ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:52.597 INFO  [http-nio-28183-exec-35] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:52.613 INFO  [http-nio-28183-exec-35] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 16ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:52.789 INFO  [http-nio-28183-exec-23] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:52.803 INFO  [http-nio-28183-exec-23] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 14ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:52.983 INFO  [http-nio-28183-exec-39] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:52.996 INFO  [http-nio-28183-exec-39] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 14ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:53.177 INFO  [http-nio-28183-exec-40] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:53.191 INFO  [http-nio-28183-exec-40] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 13ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:53.360 INFO  [http-nio-28183-exec-20] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:53.375 INFO  [http-nio-28183-exec-20] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 15ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:53.536 INFO  [http-nio-28183-exec-4] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:53.550 INFO  [http-nio-28183-exec-4] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":10,"page":1} -> 15ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]}],"errno":200,"error":"操作成功"}
2025-08-30 15:02:59.020 INFO  [http-nio-28183-exec-38] c.h.l.c.l.LogSql - Execute SQL：select * from view_police_tag_aggregation
2025-08-30 15:02:59.036 INFO  [http-nio-28183-exec-38] c.h.l.LogSlf4j - *************:/policeTagInfo/queryPersonTag (M_057021) -> {"limit":100,"page":1} -> 16ms -> {"count":52,"data":[{"idCard":"142601198803292835","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320223197803056617","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320401198103162811","tagNameList":["专家型"],"tagTypeList":["combat_ability"]},{"idCard":"320401199210122812","tagNameList":["优秀学员（提名）"],"tagTypeList":["dengfeng_training"]},{"idCard":"320402198603183412","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198210083417","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320411198811147519","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197411208114","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197612131723","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197704083618","tagNameList":["星火计划"],"tagTypeList":["xinghuo_plan"]},{"idCard":"320421197706170029","tagNameList":["月度警营先锋"],"tagTypeList":["jingying_xianfeng"]},{"idCard":"320421
2025-08-30 15:05:01.268 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 15:05:01.268 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 15:05:01.268 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 15:05:01.277 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 15:05:01.444 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-08-30 15:05:01.449 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-30 15:05:01.547 INFO  [tomcat-shutdown] o.s.b.w.e.t.GracefulShutdown - Graceful shutdown complete
2025-08-30 15:05:01.788 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-08-30 15:05:01.852 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 15:05:01.855 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 15:05:02.289 INFO  [SpringApplicationShutdownHook] c.h.c.t.GraceThreadPool - ==== 优雅关闭线程池 ====
2025-08-30 15:05:02.301 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closing ...
2025-08-30 15:05:02.333 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closed
2025-08-30 15:05:02.334 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closing ...
2025-08-30 15:05:02.340 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closed
2025-08-30 15:05:02.340 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closing ...
2025-08-30 15:05:02.348 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closed
2025-08-30 15:05:02.348 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closing ...
2025-08-30 15:05:02.354 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closed
2025-08-30 15:36:37.636 INFO  [main] c.a.n.c.e.SearchableProperties - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-30 15:36:37.689 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-30 15:36:40.388 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:36:40.388 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:36:43.817 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-30 15:36:43.826 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-30 15:36:43.830 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-30 15:36:43.832 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
2025-08-30 15:36:43.879 INFO  [main] c.h.AppMain - The following 1 profile is active: "druid"
2025-08-30 15:36:46.870 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 15:36:46.882 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-30 15:36:46.984 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 72 ms. Found 0 Elasticsearch repository interfaces.
2025-08-30 15:36:46.993 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 15:36:46.995 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-30 15:36:47.031 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-30 15:36:47.052 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 15:36:47.056 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-30 15:36:47.103 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-08-30 15:36:47.730 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:48.690 INFO  [main] o.s.c.c.s.GenericScope - BeanFactory id=aacf7363-c268-3489-b3c6-491f2c52d97b
2025-08-30 15:36:50.268 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:36:50.279 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:36:50.282 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$599/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:36:50.297 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:36:50.585 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:36:50.592 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:36:51.883 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 28183 (http)
2025-08-30 15:36:51.935 INFO  [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-28183"]
2025-08-30 15:36:51.941 INFO  [main] o.a.c.c.StandardService - Starting service [Tomcat]
2025-08-30 15:36:51.941 INFO  [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-30 15:36:52.378 INFO  [main] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 15:36:52.378 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8472 ms
2025-08-30 15:36:52.924 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 15:36:54.082 INFO  [main] c.h.d.c.MybatisPlusEnhancerConfiguration - ✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
2025-08-30 15:36:58.111 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-30 15:36:58.111 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.128 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-30 15:36:58.129 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.146 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-30 15:36:58.146 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.163 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-30 15:36:58.163 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.179 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-30 15:36:58.179 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.196 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-30 15:36:58.196 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.213 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-30 15:36:58.213 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.229 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-30 15:36:58.229 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.245 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-30 15:36:58.246 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.262 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-30 15:36:58.262 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.282 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-30 15:36:58.282 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.300 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-30 15:36:58.301 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.334 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-30 15:36:58.369 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-30 15:36:58.369 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.392 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-30 15:36:58.392 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.409 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-30 15:36:58.409 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.434 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-30 15:36:58.434 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.454 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-30 15:36:58.455 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.477 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-30 15:36:58.477 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.499 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-30 15:36:58.499 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.517 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-30 15:36:58.517 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.533 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-30 15:36:58.533 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.547 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-30 15:36:58.547 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.562 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-30 15:36:58.562 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.600 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-30 15:36:58.600 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.625 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-30 15:36:58.625 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.641 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-30 15:36:58.642 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.657 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-30 15:36:58.657 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.673 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-30 15:36:58.674 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.689 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-30 15:36:58.689 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.706 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-30 15:36:58.706 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.721 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-30 15:36:58.721 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.735 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-30 15:36:58.735 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.749 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-30 15:36:58.749 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.765 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-30 15:36:58.765 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.782 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-30 15:36:58.782 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.799 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-30 15:36:58.800 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.812 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-30 15:36:58.813 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.840 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-30 15:36:58.841 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.862 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-30 15:36:58.862 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.877 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-30 15:36:58.877 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.895 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-30 15:36:58.895 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.914 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-30 15:36:58.915 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.935 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-30 15:36:58.935 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.950 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-30 15:36:58.951 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.968 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-30 15:36:58.968 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.986 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-30 15:36:58.986 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.005 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-30 15:36:59.005 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.033 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-30 15:36:59.033 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.066 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-30 15:36:59.066 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.112 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-30 15:36:59.112 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.129 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-30 15:36:59.129 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.146 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-30 15:36:59.146 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.683 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-task' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 15:37:13.613 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:37:17.134 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:17.135 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:17.259 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:37:20.279 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:20.279 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:20.414 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:37:23.458 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:23.458 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:23.611 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:37:26.638 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:26.638 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:26.762 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:37:29.790 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:29.790 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:29.911 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:37:32.955 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:32.956 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:33.076 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:37:36.100 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:36.100 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:36.215 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:37:39.221 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:39.221 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:39.658 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
2025-08-30 15:37:39.658 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
2025-08-30 15:37:39.658 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
2025-08-30 15:37:39.658 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
2025-08-30 15:37:39.658 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
2025-08-30 15:37:39.658 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
2025-08-30 15:37:39.867 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:37:42.898 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:42.898 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:43.188 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:37:46.220 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:46.220 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:38:02.939 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 15:38:03.151 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:38:03.151 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:38:04.109 INFO  [main] c.h.n.c.SharedNacosNameSpace - {"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
2025-08-30 15:38:05.245 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 15:38:05.992 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 15:38:10.983 INFO  [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-30 15:38:12.542 INFO  [main] c.h.s.c.SecurityConfig - [/dict/api-query, /dict/add-batch, /test/dict, /dict-test/test, /error/logs] --> 200
2025-08-30 15:38:12.906 INFO  [main] o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@284022ff, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@75cf4e, org.springframework.security.web.context.SecurityContextPersistenceFilter@7203ce4f, org.springframework.security.web.header.HeaderWriterFilter@1722065e, org.springframework.security.web.authentication.logout.LogoutFilter@290f2cd5, org.springframework.web.filter.CorsFilter@76b0e1d9, com.hl.security.config.sso.SsoAuthTokenFilter@6980e66a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2ffa37e6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@41266786, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3f3e51e0, org.springframework.security.web.session.SessionManagementFilter@71025bf4, org.springframework.security.web.access.ExceptionTranslationFilter@77098877, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@29f4cdaf]
2025-08-30 15:38:20.429 INFO  [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-28183"]
2025-08-30 15:38:20.584 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 28183 (http) with context path ''
2025-08-30 15:38:21.687 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 15:38:21.693 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:38:21.693 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:38:22.000 INFO  [main] c.a.c.n.r.NacosServiceRegistry - nacos registry, default hl-wj-police-archive *************:28183 register finished
2025-08-30 15:38:25.836 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [**************:5672]
2025-08-30 15:38:26.227 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#2123416b:0/SimpleConnection@7b8aec3f [delegate=amqp://admin@**************:5672/, localPort= 63572]
2025-08-30 15:38:26.847 INFO  [main] c.h.AppMain - Started AppMain in 111.094 seconds (JVM running for 116.393)
2025-08-30 15:38:26.849 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:38:26.849 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:38:30.643 INFO  [main] c.h.s.c.s.c.SsoCache - init cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
2025-08-30 15:38:30.649 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:38:30.650 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:38:30.916 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
2025-08-30 15:38:30.917 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
2025-08-30 15:38:30.917 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
2025-08-30 15:38:30.991 INFO  [main] c.h.d.s.i.DictDataServiceImpl - refresh dict cache 
2025-08-30 15:38:32.052 INFO  [RMI TCP Connection(6)-*************] o.a.c.c.C.[.[.[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 15:38:32.052 INFO  [RMI TCP Connection(6)-*************] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 15:38:32.095 INFO  [RMI TCP Connection(6)-*************] o.s.w.s.DispatcherServlet - Completed initialization in 43 ms
2025-08-30 15:38:32.240 INFO  [main] c.h.l.c.l.LogSql - Execute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
2025-08-30 15:38:33.054 WARN  [RMI TCP Connection(4)-*************] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor142.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-30 15:38:39.431 ERROR [main] c.a.d.f.s.StatFilter - slow sql 2008 millis. SELECT 1[]
2025-08-30 15:38:42.989 INFO  [main] c.a.d.p.DruidDataSource - {dataSource-1} inited
2025-08-30 15:38:47.412 INFO  [RMI TCP Connection(4)-*************] c.a.d.p.DruidDataSource - {dataSource-2} inited
2025-08-30 15:38:52.038 INFO  [RMI TCP Connection(4)-*************] c.a.d.p.DruidDataSource - {dataSource-3} inited
2025-08-30 15:38:57.468 INFO  [RMI TCP Connection(4)-*************] c.a.d.p.DruidDataSource - {dataSource-4} inited
2025-08-30 15:41:43.002 INFO  [rebel-change-detector-thread] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-30 15:41:43.013 INFO  [rebel-change-detector-thread] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Elasticsearch repository interfaces.
2025-08-30 15:41:43.025 INFO  [rebel-change-detector-thread] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-30 15:41:43.028 INFO  [rebel-change-detector-thread] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-30 15:41:43.031 WARN  [rebel-change-detector-thread] o.m.s.m.ClassPathMapperScanner - No MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
2025-08-30 15:41:43.036 INFO  [rebel-change-detector-thread] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-30 15:41:43.039 INFO  [rebel-change-detector-thread] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-08-30 15:44:20.114 ERROR [http-nio-28183-exec-1] c.h.c.c.e.GlobalExceptionHandler - 请求地址'/clubActivity/approveActivityTask',发生系统异常 -->> org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoSuchMethodError: com.hl.archive.domain.dto.PoliceClubActivityApproveTaskDTO.getCjrs()Ljava/lang/Integer;
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53)
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52)
Caused by: java.lang.NoSuchMethodError: com.hl.archive.domain.dto.PoliceClubActivityApproveTaskDTO.getCjrs()Ljava/lang/Integer;
	at com.hl.log.LogSlf4j.handler(LogSlf4j.java:130)
	at com.hl.log.LogSlf4j.doAfterReturning(LogSlf4j.java:56)
	at com.hl.archive.controller.PoliceClubActivityController$$EnhancerBySpringCGLIB$$1.approveActivityTask(<generated>)
2025-08-30 15:44:41.738 WARN  [http-nio-28183-exec-3] o.s.c.l.c.RoundRobinLoadBalancer - No servers available for service: hl-task
2025-08-30 15:44:41.744 WARN  [http-nio-28183-exec-3] o.s.c.o.l.RetryableFeignBlockingLoadBalancerClient - Service instance was not resolved, executing the original request
2025-08-30 15:44:44.472 ERROR [http-nio-28183-exec-3] c.h.c.c.e.GlobalExceptionHandler - 请求地址'/clubActivity/approveActivityTask',发生系统异常 -->> org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoSuchMethodError: com.hl.archive.domain.dto.PoliceClubActivityApproveTaskDTO.getCjrs()Ljava/lang/Integer;
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53)
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52)
Caused by: java.lang.NoSuchMethodError: com.hl.archive.domain.dto.PoliceClubActivityApproveTaskDTO.getCjrs()Ljava/lang/Integer;
	at com.hl.log.LogSlf4j.handler(LogSlf4j.java:130)
	at com.hl.log.LogSlf4j.doAfterThrowing(LogSlf4j.java:46)
	at com.hl.archive.controller.PoliceClubActivityController$$EnhancerBySpringCGLIB$$1.approveActivityTask(<generated>)
2025-08-30 15:45:16.011 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 15:45:16.011 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 15:45:16.011 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 15:45:16.020 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 15:45:16.211 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-08-30 15:45:16.213 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-30 15:45:16.271 INFO  [tomcat-shutdown] o.s.b.w.e.t.GracefulShutdown - Graceful shutdown complete
2025-08-30 15:45:17.120 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-08-30 15:45:17.179 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 15:45:17.184 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 15:45:17.576 INFO  [SpringApplicationShutdownHook] c.h.c.t.GraceThreadPool - ==== 优雅关闭线程池 ====
2025-08-30 15:45:17.601 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closing ...
2025-08-30 15:45:17.675 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closed
2025-08-30 15:45:17.676 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closing ...
2025-08-30 15:45:17.684 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closed
2025-08-30 15:45:17.684 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closing ...
2025-08-30 15:45:17.691 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closed
2025-08-30 15:45:17.693 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closing ...
2025-08-30 15:45:17.702 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closed
2025-08-30 15:45:26.764 INFO  [main] c.a.n.c.e.SearchableProperties - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-30 15:45:26.816 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-30 15:45:29.460 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:45:29.462 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:45:32.767 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-30 15:45:32.775 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-30 15:45:32.780 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-30 15:45:32.782 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
2025-08-30 15:45:32.828 INFO  [main] c.h.AppMain - The following 1 profile is active: "druid"
2025-08-30 15:45:35.709 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 15:45:35.720 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-30 15:45:35.826 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 76 ms. Found 0 Elasticsearch repository interfaces.
2025-08-30 15:45:35.835 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 15:45:35.836 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-30 15:45:35.888 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 51 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-30 15:45:35.907 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-30 15:45:35.912 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-30 15:45:35.958 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Redis repository interfaces.
2025-08-30 15:45:36.562 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.562 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:37.497 INFO  [main] o.s.c.c.s.GenericScope - BeanFactory id=aacf7363-c268-3489-b3c6-491f2c52d97b
2025-08-30 15:45:39.044 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:45:39.055 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:45:39.059 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$599/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:45:39.073 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:45:39.395 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:45:39.419 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 15:45:40.685 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 28183 (http)
2025-08-30 15:45:40.732 INFO  [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-28183"]
2025-08-30 15:45:40.738 INFO  [main] o.a.c.c.StandardService - Starting service [Tomcat]
2025-08-30 15:45:40.738 INFO  [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-30 15:45:41.151 INFO  [main] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 15:45:41.152 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8297 ms
2025-08-30 15:45:41.682 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 15:45:42.730 INFO  [main] c.h.d.c.MybatisPlusEnhancerConfiguration - ✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
2025-08-30 15:45:46.722 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-30 15:45:46.722 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.752 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-30 15:45:46.753 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.770 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-30 15:45:46.770 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.787 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-30 15:45:46.787 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.802 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-30 15:45:46.803 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.819 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-30 15:45:46.819 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.836 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-30 15:45:46.837 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.851 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-30 15:45:46.852 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.867 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-30 15:45:46.867 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.884 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-30 15:45:46.884 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.901 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-30 15:45:46.901 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.919 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-30 15:45:46.919 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.937 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-30 15:45:46.971 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-30 15:45:46.971 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.992 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-30 15:45:46.992 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.009 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-30 15:45:47.009 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.025 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-30 15:45:47.025 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.045 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-30 15:45:47.045 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.065 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-30 15:45:47.065 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.087 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-30 15:45:47.087 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.106 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-30 15:45:47.106 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.120 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-30 15:45:47.121 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.136 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-30 15:45:47.136 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.150 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-30 15:45:47.151 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.186 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-30 15:45:47.186 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.211 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-30 15:45:47.211 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.226 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-30 15:45:47.227 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.241 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-30 15:45:47.241 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.258 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-30 15:45:47.258 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.274 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-30 15:45:47.274 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.291 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-30 15:45:47.291 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.305 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-30 15:45:47.305 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.319 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-30 15:45:47.319 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.334 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-30 15:45:47.334 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.348 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-30 15:45:47.348 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.364 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-30 15:45:47.365 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.381 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-30 15:45:47.381 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.395 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-30 15:45:47.395 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.423 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-30 15:45:47.423 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.444 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-30 15:45:47.444 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.459 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-30 15:45:47.459 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.477 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-30 15:45:47.477 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.495 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-30 15:45:47.495 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.512 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-30 15:45:47.512 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.528 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-30 15:45:47.528 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.543 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-30 15:45:47.543 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.559 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-30 15:45:47.560 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.576 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-30 15:45:47.576 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.603 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-30 15:45:47.604 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.634 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-30 15:45:47.634 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.663 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-30 15:45:47.663 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.681 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-30 15:45:47.681 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.698 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-30 15:45:47.698 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:48.239 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-task' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 15:46:03.267 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:46:06.797 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:06.798 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:06.914 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:46:09.949 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:09.949 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:10.083 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:46:14.135 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:14.135 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:14.270 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:46:17.310 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:17.310 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:17.452 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:46:20.494 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:20.495 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:20.632 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:46:23.669 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:23.669 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:23.812 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:46:26.843 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:26.843 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:26.974 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:46:30.004 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:30.004 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:30.466 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
2025-08-30 15:46:30.466 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
2025-08-30 15:46:30.466 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
2025-08-30 15:46:30.466 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
2025-08-30 15:46:30.466 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
2025-08-30 15:46:30.467 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
2025-08-30 15:46:30.670 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:46:33.707 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:33.709 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:34.012 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-30 15:46:37.036 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:37.037 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:55.576 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 15:46:55.787 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:46:55.787 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:46:56.712 INFO  [main] c.h.n.c.SharedNacosNameSpace - {"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
2025-08-30 15:46:57.836 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 15:46:58.575 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
2025-08-30 15:47:03.553 INFO  [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-30 15:47:05.085 INFO  [main] c.h.s.c.SecurityConfig - [/error/logs, /dict/add-batch, /dict/api-query, /dict-test/test, /test/dict] --> 200
2025-08-30 15:47:05.442 INFO  [main] o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@213de840, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5b58ae7, org.springframework.security.web.context.SecurityContextPersistenceFilter@7c3d3077, org.springframework.security.web.header.HeaderWriterFilter@4867cf6b, org.springframework.security.web.authentication.logout.LogoutFilter@7ed0df52, org.springframework.web.filter.CorsFilter@5c6596b4, com.hl.security.config.sso.SsoAuthTokenFilter@1985f8e3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@23a2bbb9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b3d0ec5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@25de1b13, org.springframework.security.web.session.SessionManagementFilter@6cce41d8, org.springframework.security.web.access.ExceptionTranslationFilter@3afd8e57, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7b0af06]
2025-08-30 15:47:12.803 INFO  [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-28183"]
2025-08-30 15:47:12.950 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 28183 (http) with context path ''
2025-08-30 15:47:14.052 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-30 15:47:14.059 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:47:14.059 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:47:14.342 INFO  [main] c.a.c.n.r.NacosServiceRegistry - nacos registry, default hl-wj-police-archive *************:28183 register finished
2025-08-30 15:47:18.836 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [**************:5672]
2025-08-30 15:47:19.431 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#150a6caf:0/SimpleConnection@520822f3 [delegate=amqp://admin@**************:5672/, localPort= 59002]
2025-08-30 15:47:20.073 INFO  [main] c.h.AppMain - Started AppMain in 115.213 seconds (JVM running for 120.86)
2025-08-30 15:47:20.075 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:47:20.075 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:47:24.001 INFO  [main] c.h.s.c.s.c.SsoCache - init cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
2025-08-30 15:47:24.008 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-30 15:47:24.008 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-30 15:47:24.276 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
2025-08-30 15:47:24.277 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
2025-08-30 15:47:24.277 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
2025-08-30 15:47:24.353 INFO  [main] c.h.d.s.i.DictDataServiceImpl - refresh dict cache 
2025-08-30 15:47:24.954 INFO  [RMI TCP Connection(5)-*************] o.a.c.c.C.[.[.[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 15:47:24.963 INFO  [RMI TCP Connection(5)-*************] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 15:47:24.983 INFO  [RMI TCP Connection(5)-*************] o.s.w.s.DispatcherServlet - Completed initialization in 20 ms
2025-08-30 15:47:25.827 INFO  [main] c.h.l.c.l.LogSql - Execute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
2025-08-30 15:47:25.999 WARN  [RMI TCP Connection(4)-*************] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-30 15:47:32.599 ERROR [main] c.a.d.f.s.StatFilter - slow sql 1786 millis. SELECT 1[]
2025-08-30 15:47:36.199 INFO  [main] c.a.d.p.DruidDataSource - {dataSource-1} inited
2025-08-30 15:47:36.309 WARN  [RMI TCP Connection(4)-*************] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 10140ms to respond
2025-08-30 15:47:40.659 INFO  [RMI TCP Connection(4)-*************] c.a.d.p.DruidDataSource - {dataSource-2} inited
2025-08-30 15:47:45.056 INFO  [RMI TCP Connection(4)-*************] c.a.d.p.DruidDataSource - {dataSource-3} inited
2025-08-30 15:47:50.487 INFO  [RMI TCP Connection(4)-*************] c.a.d.p.DruidDataSource - {dataSource-4} inited
2025-08-30 15:48:37.325 INFO  [http-nio-28183-exec-1] c.h.l.LogSlf4j - *************:/clubActivity/approveActivityTask (M_057021) -> {"cjrs":0,"pass":1,"taskId":"1756538490329J66RI","text":"11111"} -> 7759ms -> {"errno":500,"error":"参加人员超过允许参加人数 不允许审核通过"}
2025-08-30 15:49:27.951 INFO  [http-nio-28183-exec-21] c.h.l.LogSlf4j - *************:/clubActivity/approveActivityTask (M_057021) -> {"cjrs":10,"pass":1,"taskId":"1756538490329J66RI","text":"11111"} -> 19597ms -> {"errno":500,"error":"您没有权限审批该任务"}
2025-08-30 15:49:56.575 INFO  [http-nio-28183-exec-42] c.h.l.LogSlf4j - *************:/clubActivity/approveActivityTask (M_057021) -> {"cjrs":10,"pass":1,"taskId":"1756538490329J66RI","text":"11111"} -> 7441ms -> {"errno":500,"error":"您没有权限审批该任务"}
2025-08-30 15:52:22.094 WARN  [http-nio-28183-exec-24] o.m.s.m.ClassPathMapperScanner - No MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
2025-08-30 15:52:22.094 INFO  [http-nio-28183-exec-24] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-30 15:52:22.107 INFO  [http-nio-28183-exec-24] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-08-30 15:52:22.110 INFO  [http-nio-28183-exec-24] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-30 15:52:22.114 INFO  [http-nio-28183-exec-24] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-30 15:52:22.117 INFO  [http-nio-28183-exec-24] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-30 15:52:22.121 INFO  [http-nio-28183-exec-24] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Elasticsearch repository interfaces.
2025-08-30 15:52:23.262 WARN  [http-nio-28183-exec-24] o.s.c.l.c.RoundRobinLoadBalancer - No servers available for service: hl-task
2025-08-30 15:52:23.269 WARN  [http-nio-28183-exec-24] o.s.c.o.l.RetryableFeignBlockingLoadBalancerClient - Service instance was not resolved, executing the original request
2025-08-30 15:52:26.080 INFO  [http-nio-28183-exec-24] c.h.l.LogSlf4j - *************:/clubActivity/approveActivityTask (M_057021) -> {"cjrs":10,"pass":1,"taskId":"1756538490329J66RI","text":"11111"} -> 2853ms -> {"cause":{"localizedMessage":"hl-task","message":"hl-task","stackTrace":[{"fileName":"AbstractPlainSocketImpl.java","lineNumber":184,"className":"java.net.AbstractPlainSocketImpl","methodName":"connect"},{"fileName":"PlainSocketImpl.java","lineNumber":172,"className":"java.net.PlainSocketImpl","methodName":"connect"},{"fileName":"SocksSocketImpl.java","lineNumber":392,"className":"java.net.SocksSocketImpl","methodName":"connect"},{"fileName":"Socket.java","lineNumber":607,"className":"java.net.Socket","methodName":"connect"},{"fileName":"NetworkClient.java","lineNumber":175,"className":"sun.net.NetworkClient","methodName":"doConnect"},{"fileName":"HttpClient.java","lineNumber":465,"className":"sun.net.www.http.HttpClient","methodName":"openServer"},{"fileName":"HttpClient.java","lineNumber":560,"className":"sun.net.www.http.HttpClient","methodName":"openServer"},{"fileName":"HttpClient.java","lineNumber":244,"className":"sun.net.www.http.HttpClient","methodName":"<init>"},{"fileName":"
2025-08-30 15:52:26.092 ERROR [http-nio-28183-exec-24] c.h.c.c.e.GlobalExceptionHandler - /clubActivity/approveActivityTask --> 
feign.RetryableException: hl-task executing POST http://hl-task/task/info/one
	at feign.FeignException.errorExecuting(FeignException.java:249) ~[feign-core-10.12.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:129) ~[feign-core-10.12.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89) ~[feign-core-10.12.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-10.12.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.5.jar:3.1.5]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.5.jar:3.1.5]
	at com.sun.proxy.$Proxy206.getOneTask(Unknown Source) ~[?:?]
	at com.hl.archive.service.PoliceClubActivityService.approveActivityTask(PoliceClubActivityService.java:250) ~[classes/:?]
	at com.hl.archive.service.PoliceClubActivityService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceClubActivityService$$EnhancerBySpringCGLIB$$1.approveActivityTask(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceClubActivityController.approveActivityTask(PoliceClubActivityController.java:72) ~[classes/:?]
	at com.hl.archive.controller.PoliceClubActivityController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceClubActivityController$$EnhancerBySpringCGLIB$$1.approveActivityTask(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.UnknownHostException: hl-task
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:184) ~[?:1.8.0_432-432]
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172) ~[?:1.8.0_432-432]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392) ~[?:1.8.0_432-432]
	at java.net.Socket.connect(Socket.java:607) ~[?:1.8.0_432-432]
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175) ~[?:1.8.0_432-432]
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:465) ~[?:1.8.0_432-432]
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:560) ~[?:1.8.0_432-432]
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:244) ~[?:1.8.0_432-432]
	at sun.net.www.http.HttpClient.New(HttpClient.java:341) ~[?:1.8.0_432-432]
	at sun.net.www.http.HttpClient.New(HttpClient.java:359) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1243) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1177) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1071) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1005) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1357) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1332) ~[?:1.8.0_432-432]
	at feign.Client$Default.convertAndSend(Client.java:202) ~[feign-core-10.12.jar:?]
	at feign.Client$Default.execute(Client.java:103) ~[feign-core-10.12.jar:?]
	at org.springframework.cloud.openfeign.loadbalancer.LoadBalancerUtils.executeWithLoadBalancerLifecycleProcessing(LoadBalancerUtils.java:57) ~[spring-cloud-openfeign-core-3.1.5.jar:3.1.5]
	at org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient.lambda$execute$2(RetryableFeignBlockingLoadBalancerClient.java:168) ~[spring-cloud-openfeign-core-3.1.5.jar:3.1.5]
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:329) ~[spring-retry-1.3.4.jar:?]
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:225) ~[spring-retry-1.3.4.jar:?]
	at org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient.execute(RetryableFeignBlockingLoadBalancerClient.java:114) ~[spring-cloud-openfeign-core-3.1.5.jar:3.1.5]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119) ~[feign-core-10.12.jar:?]
	... 139 more
2025-08-30 15:53:57.386 INFO  [http-nio-28183-exec-26] c.h.a.s.PoliceClubActivityService - 社团活动审批传参: {task_id=1756538490329J66RI, node_id=ZZCSP, content={pass=1, text=11111, rwsj=[{cjrs=10}]}}
2025-08-30 15:54:07.309 INFO  [http-nio-28183-exec-26] c.h.l.LogSlf4j - *************:/clubActivity/approveActivityTask (M_057021) -> {"cjrs":10,"pass":1,"taskId":"1756538490329J66RI","text":"11111"} -> 10496ms -> {"errno":500,"error":"您没有权限审批该任务"}
2025-08-30 15:57:06.496 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.TaskEventListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"audit\",\"data\":{\"content\":{\"pass\":1,\"text\":\"111\",\"FormCompositeComponent_itnevKNPDdL7oQd_\":[{\"FormInput_swZLNKAl_l3AOW1O\":\"1111\",\"cjrs\":\"1111\"}],\"id_card\":\"320483199002102616\"},\"node_id\":\"RceKzYYIFglitS2l\",\"result_id\":\"\",\"task_id\":\"1756532500414CITBE\",\"id_card\":\"320483199002102616\",\"config_uuid\":\"CXQNJMIAR1C\",\"type\":\"audit\",\"time\":\"2025-08-30 15:57:15\",\"form_field_list\":[{\"_style\":{},\"_property\":{\"editable\":true,\"label\":\"是否通过\",\"label_width\":\"\",\"label_id\":\"\",\"placeholder\":\"\",\"expand\":true,\"guide\":\"\",\"format\":\"\",\"required\":true,\"readonly\":true,\"no_sign_use_police\":0,\"is_digital_signature\":0,\"is_use_seal\":0,\"default_value\":{\"type\":\"custome\",\"value\":\"\"},\"options\":[{\"label\":\"通过\",\"value\":1},{\"label\":\"驳回\",\"value\":0}],\"status\":1},\"id\":\"pass\",\"key\":\"FormSelect\",\"notSubmit\":false},{\"_style\":{},\"_property\":{\"editable\":true,\"label\":\"审批说明\",\"label_width\":\"\",\"label_id\":\"\",\"placeholder\":\"\",\"guide\":\"\",\"format\":\"\",\"required\":false,\"readonly\":true,\"default_value\":{\"type\":\"custome\",\"value\":\"\"},\"status\":1},\"id\":\"text\",\"key\":\"FormTextarea\",\"notSubmit\":false},{\"deleteBottom\":\"0px\",\"_style\":{\"height\":\"auto\",\"minHeight\":40},\"_property\":{\"show\":[],\"label\":\"\",\"label_id\":\"\",\"the_a\":\"第*行\",\"composition_type\":\"card\",\"multiple_feedback\":1,\"maxmin\":\"\",\"url\":\"/task/api/task/info/one?task_id=${taskId}\",\"port_format\":\"post\",\"call_chain\":\"data.all_content\",\"is_add\":0,\"is_delete\":1,\"guide\":\"\"},\"id\":\"FormCompositeComponent_itnevKNPDdL7oQd_\",\"key\":\"FormCompositeComponent\",\"children\":[{\"_style\":{},\"_property\":{\"editable\":true,\"label\":\"参加人数\",\"label_width\":\"\",\"label_id\":\"cjrs\",\"placeholder\":\"\",\"guide\":\"\",\"show\":[{\"id\":\"DHTW5AGZk2nBGxHb\",\"type\":\"condition\",\"field\":[\"task_comp_mode\"],\"opt\":\"equal\",\"value\":\"preview\"}],\"format\":\"\",\"prefix_text\":\"\",\"required\":true,\"maxmin\":\"\",\"astrict_scope\":{},\"default_value\":{\"type\":\"custome\",\"value\":\"\"},\"status\":1,\"relevance_field\":\"\",\"calc_type\":\"\",\"is_allow_copy\":0,\"is_allow_jump\":0,\"jump_path\":\"\",\"is_default_search\":0,\"is_form_person\":0},\"id\":\"FormInput_swZLNKAl_l3AOW1O\",\"key\":\"FormInput\"}]}],\"custom_id\":\"ZZCSP\"}}","content_time":"2025-08-30 15:57:17","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"922e32ec-adc6-4f9f-ae76-90c618f06a02"}
2025-08-30 15:57:06.863 INFO  [pool-20-thread-1] c.h.a.l.TaskEventListener - 使用策略处理任务: configUuid=CXQNJMIAR1C, opt=audit, strategy=PoliceClubActivityStrategy
2025-08-30 15:57:06.863 INFO  [pool-20-thread-1] c.h.a.l.s.i.PoliceClubActivityStrategy - 处理社团活动任务，操作类型: audit
2025-08-30 15:57:29.630 INFO  [pool-20-thread-1] c.h.l.c.l.LogSql - Execute SQL：INSERT INTO police_club_activity ( club_id, activity_time, `location`, participants, advance_payment, created_by, activity_name, activity_budget, task_id, activity_notice, activity_plan, budget_detail, allowed_participant_count ) VALUES ( 2, '2025-08-01T00:00', '222', '[******************, 431103199002127896, 32021919820923103X]', '0', 'hualong', '22222', '', '1756532500414CITBE', '[{"id":"N1756532492061Bv","name":"全息人员档案政工平台字段0605.xls","path":"2025/8/30/N1756532492061Bv.xls"}]', '[{"id":"N1756532495320lo","name":"全息人员档案政工平台字段0605.xls","path":"2025/8/30/N1756532495320lo.xls"}]', '', 1111 )
2025-08-30 15:57:30.035 INFO  [pool-20-thread-1] c.h.a.s.PoliceClubActivityService - 社团活动保存成功:PoliceClubActivity(id=9, clubId=2, clubInfo=null, activityTime=2025-08-01T00:00, location=222, participants=[******************, 431103199002127896, 32021919820923103X], participantsInfo=null, attachment=null, media=null, advancePayment=0, createTime=null, updateTime=null, isDeleted=null, createdBy=hualong, updatedBy=null, activityName=22222, activityBudget=, taskId=1756532500414CITBE, activityNotice=[{"id":"N1756532492061Bv","name":"全息人员档案政工平台字段0605.xls","path":"2025/8/30/N1756532492061Bv.xls"}], activityPlan=[{"id":"N1756532495320lo","name":"全息人员档案政工平台字段0605.xls","path":"2025/8/30/N1756532495320lo.xls"}], budgetDetail=, allowedParticipantCount=1111, activityStatus=null)
2025-08-30 16:00:02.889 INFO  [scheduling-1] c.h.s.c.s.c.SsoCache - init cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
2025-08-30 16:02:25.343 WARN  [http-nio-28183-exec-45] o.m.s.m.ClassPathMapperScanner - No MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
2025-08-30 16:02:25.343 INFO  [http-nio-28183-exec-45] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-30 16:02:25.351 INFO  [http-nio-28183-exec-45] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-08-30 16:02:25.356 INFO  [http-nio-28183-exec-45] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-30 16:02:25.358 INFO  [http-nio-28183-exec-45] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 2 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-30 16:02:25.361 INFO  [http-nio-28183-exec-45] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-30 16:02:25.364 INFO  [http-nio-28183-exec-45] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 2 ms. Found 0 Elasticsearch repository interfaces.
2025-08-30 16:02:26.384 INFO  [http-nio-28183-exec-45] c.h.a.s.PoliceClubActivityService - 社团活动审批传参: {task_id=1756521889460G0LC3, content={pass=1, text=11111, rwsj=[{cjrs=10}], sign_url=057021}}
2025-08-30 16:02:29.058 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.TaskEventListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"audit\",\"data\":{\"content\":{\"pass\":1,\"text\":\"11111\",\"rwsj\":[{\"cjrs\":10}],\"sign_url\":\"057021\",\"id_card\":\"320483199002102616\"},\"task_id\":\"1756521889460G0LC3\",\"id_card\":\"320483199002102616\",\"config_uuid\":\"CXQNJMIAR1C\",\"type\":\"audit\",\"time\":\"2025-08-30 16:02:38\",\"form_field_list\":[{\"_style\":{},\"_property\":{\"editable\":true,\"label\":\"是否通过\",\"label_width\":\"\",\"label_id\":\"\",\"placeholder\":\"\",\"expand\":true,\"guide\":\"\",\"format\":\"\",\"required\":true,\"readonly\":true,\"no_sign_use_police\":0,\"is_digital_signature\":0,\"is_use_seal\":0,\"default_value\":{\"type\":\"custome\",\"value\":\"\"},\"options\":[{\"label\":\"通过\",\"value\":1},{\"label\":\"驳回\",\"value\":0}],\"status\":1},\"id\":\"pass\",\"key\":\"FormSelect\",\"notSubmit\":false},{\"_style\":{},\"_property\":{\"editable\":true,\"label\":\"审批说明\",\"label_width\":\"\",\"label_id\":\"\",\"placeholder\":\"\",\"guide\":\"\",\"format\":\"\",\"required\":false,\"readonly\":true,\"default_value\":{\"type\":\"custome\",\"value\":\"\"},\"status\":1},\"id\":\"text\",\"key\":\"FormTextarea\",\"notSubmit\":false},{\"deleteBottom\":\"0px\",\"_style\":{\"height\":\"auto\",\"minHeight\":40},\"_property\":{\"show\":[],\"label\":\"\",\"label_id\":\"\",\"the_a\":\"第*行\",\"composition_type\":\"card\",\"multiple_feedback\":1,\"maxmin\":\"\",\"url\":\"/task/api/task/info/one?task_id=${taskId}\",\"port_format\":\"post\",\"call_chain\":\"data.all_content\",\"is_add\":1,\"is_delete\":1,\"guide\":\"\"},\"id\":\"FormCompositeComponent_itnevKNPDdL7oQd_\",\"key\":\"FormCompositeComponent\",\"children\":[{\"_style\":{},\"_property\":{\"editable\":true,\"label\":\"参加人数\",\"label_width\":\"\",\"label_id\":\"cjrs\",\"placeholder\":\"\",\"guide\":\"\",\"show\":[],\"format\":\"\",\"prefix_text\":\"\",\"required\":true,\"maxmin\":\"\",\"astrict_scope\":{},\"default_value\":{\"type\":\"custome\",\"value\":\"\"},\"status\":1,\"relevance_field\":\"\",\"calc_type\":\"\",\"is_allow_copy\":0,\"is_allow_jump\":0,\"jump_path\":\"\",\"is_default_search\":0,\"is_form_person\":0},\"id\":\"FormInput_swZLNKAl_l3AOW1O\",\"key\":\"FormInput\"}]}],\"custom_id\":\"ZZCSP\"}}","content_time":"2025-08-30 16:02:40","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"f36740a0-0dd8-46d1-b5aa-c456b41cc660"}
2025-08-30 16:02:29.061 INFO  [pool-20-thread-2] c.h.a.l.TaskEventListener - 使用策略处理任务: configUuid=CXQNJMIAR1C, opt=audit, strategy=PoliceClubActivityStrategy
2025-08-30 16:02:29.061 INFO  [pool-20-thread-2] c.h.a.l.s.i.PoliceClubActivityStrategy - 处理社团活动任务，操作类型: audit
2025-08-30 16:02:38.330 INFO  [http-nio-28183-exec-45] c.h.l.LogSlf4j - *************:/clubActivity/approveActivityTask (M_057021) -> {"cjrs":10,"pass":1,"taskId":"1756521889460G0LC3","text":"11111"} -> 12343ms -> {"errno":200,"error":"操作成功"}
2025-08-30 16:02:38.349 INFO  [pool-20-thread-2] c.h.l.c.l.LogSql - Execute SQL：INSERT INTO police_club_activity ( club_id, activity_time, `location`, participants, advance_payment, created_by, activity_budget, task_id, activity_notice, activity_plan, budget_detail, allowed_participant_count ) VALUES ( 1, '2025-08-22T00:00', '1', '[******************, 32011111111111111, 320401199210122812]', '1', '320483199311161513', '1', '1756521889460G0LC3', '[{"id":"N1756521864675rf","name":"测试.xlsx","path":"2025/8/30/N1756521864675rf.xlsx"}]', '[{"id":"N1756521868400Sq","name":"测试.xlsx","path":"2025/8/30/N1756521868400Sq.xlsx"}]', '1', 3 )
2025-08-30 16:02:38.471 INFO  [pool-20-thread-2] c.h.a.s.PoliceClubActivityService - 社团活动保存成功:PoliceClubActivity(id=11, clubId=1, clubInfo=null, activityTime=2025-08-22T00:00, location=1, participants=[******************, 32011111111111111, 320401199210122812], participantsInfo=null, attachment=null, media=null, advancePayment=1, createTime=null, updateTime=null, isDeleted=null, createdBy=320483199311161513, updatedBy=null, activityName=null, activityBudget=1, taskId=1756521889460G0LC3, activityNotice=[{"id":"N1756521864675rf","name":"测试.xlsx","path":"2025/8/30/N1756521864675rf.xlsx"}], activityPlan=[{"id":"N1756521868400Sq","name":"测试.xlsx","path":"2025/8/30/N1756521868400Sq.xlsx"}], budgetDetail=1, allowedParticipantCount=3, activityStatus=null)
2025-08-30 16:03:25.566 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 16:03:25.566 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 16:03:25.616 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 16:03:25.645 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 16:03:25.679 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-08-30 16:03:25.681 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-30 16:03:25.739 INFO  [tomcat-shutdown] o.s.b.w.e.t.GracefulShutdown - Graceful shutdown complete
2025-08-30 16:03:25.863 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-08-30 16:03:25.924 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 16:03:25.929 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 16:03:26.320 INFO  [SpringApplicationShutdownHook] c.h.c.t.GraceThreadPool - ==== 优雅关闭线程池 ====
2025-08-30 16:03:26.345 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closing ...
2025-08-30 16:03:26.354 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closed
2025-08-30 16:03:26.355 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closing ...
2025-08-30 16:03:26.370 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closed
2025-08-30 16:03:26.371 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closing ...
2025-08-30 16:03:26.373 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closed
2025-08-30 16:03:26.374 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closing ...
2025-08-30 16:03:26.376 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closed
