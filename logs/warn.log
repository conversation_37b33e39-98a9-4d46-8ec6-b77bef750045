2025-08-29 13:33:52.923 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-29 13:33:52.932 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-29 13:33:52.936 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-29 13:33:57.292 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. <PERSON> already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:34:08.945 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-29 13:34:08.945 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:08.964 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-29 13:34:08.965 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:08.983 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-29 13:34:08.983 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.002 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-29 13:34:09.002 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.017 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-29 13:34:09.018 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.036 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-29 13:34:09.036 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.054 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-29 13:34:09.054 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.071 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-29 13:34:09.071 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.089 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-29 13:34:09.089 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.106 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-29 13:34:09.106 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.123 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-29 13:34:09.124 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.143 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-29 13:34:09.143 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.163 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-29 13:34:09.199 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-29 13:34:09.199 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.224 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-29 13:34:09.225 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.240 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-29 13:34:09.240 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.257 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-29 13:34:09.257 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.278 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-29 13:34:09.278 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.297 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-29 13:34:09.297 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.318 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-29 13:34:09.319 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.336 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-29 13:34:09.337 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.354 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-29 13:34:09.354 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.369 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-29 13:34:09.369 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.385 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-29 13:34:09.385 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.437 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-29 13:34:09.437 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.463 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-29 13:34:09.463 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.478 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-29 13:34:09.478 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.492 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-29 13:34:09.493 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.510 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-29 13:34:09.511 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.525 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-29 13:34:09.526 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.541 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-29 13:34:09.542 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.556 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-29 13:34:09.557 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.571 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-29 13:34:09.572 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.588 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-29 13:34:09.588 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.603 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-29 13:34:09.603 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.618 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-29 13:34:09.619 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.634 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-29 13:34:09.634 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.650 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-29 13:34:09.650 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.680 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-29 13:34:09.680 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.702 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-29 13:34:09.702 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.720 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-29 13:34:09.720 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.740 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-29 13:34:09.740 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.759 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-29 13:34:09.759 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.777 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-29 13:34:09.777 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.795 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-29 13:34:09.795 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.810 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-29 13:34:09.811 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.827 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-29 13:34:09.828 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.844 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-29 13:34:09.844 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.873 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-29 13:34:09.873 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.905 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-29 13:34:09.905 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.935 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-29 13:34:09.935 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.953 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-29 13:34:09.954 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.971 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-29 13:34:09.971 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:27.574 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:27.575 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:30.734 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:30.735 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:33.901 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:33.901 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:37.058 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:37.058 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:40.218 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:40.218 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:43.773 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:43.774 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:46.950 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:46.950 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:51.101 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:51.101 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:54.787 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:54.787 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:58.416 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:58.416 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:35:47.383 WARN  [RMI TCP Connection(14)-100.88.176.35] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor142.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-29 13:35:55.204 ERROR [RMI TCP Connection(14)-100.88.176.35] c.a.d.f.s.StatFilter - slow sql 1838 millis. SELECT 1[]
2025-08-29 13:35:58.850 WARN  [RMI TCP Connection(14)-100.88.176.35] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 11242ms to respond
2025-08-29 13:36:25.268 WARN  [RMI TCP Connection(14)-100.88.176.35] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration$AdaptedReactiveHealthContributors$1 (redis) took 12318ms to respond
2025-08-29 13:45:42.488 WARN  [Thread-32] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-29 13:45:42.488 WARN  [Thread-32] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-29 13:45:42.488 WARN  [Thread-26] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-29 13:45:42.495 WARN  [Thread-26] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 14:51:08.874 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-30 14:51:08.884 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-30 14:51:08.889 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-30 14:51:13.184 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.184 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.184 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:13.185 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:51:23.853 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-30 14:51:23.854 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.872 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-30 14:51:23.872 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.890 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-30 14:51:23.890 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.907 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-30 14:51:23.907 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.923 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-30 14:51:23.923 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.940 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-30 14:51:23.940 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.957 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-30 14:51:23.957 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.972 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-30 14:51:23.973 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:23.991 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-30 14:51:23.991 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.008 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-30 14:51:24.008 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.026 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-30 14:51:24.026 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.044 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-30 14:51:24.044 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.062 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-30 14:51:24.098 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-30 14:51:24.098 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.122 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-30 14:51:24.122 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.141 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-30 14:51:24.141 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.162 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-30 14:51:24.162 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.181 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-30 14:51:24.181 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.200 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-30 14:51:24.200 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.222 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-30 14:51:24.222 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.241 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-30 14:51:24.241 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.257 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-30 14:51:24.257 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.272 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-30 14:51:24.272 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.288 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-30 14:51:24.289 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.326 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-30 14:51:24.326 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.352 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-30 14:51:24.352 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.368 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-30 14:51:24.369 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.385 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-30 14:51:24.386 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.403 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-30 14:51:24.403 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.419 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-30 14:51:24.419 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.436 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-30 14:51:24.436 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.451 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-30 14:51:24.451 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.465 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-30 14:51:24.465 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.480 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-30 14:51:24.480 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.498 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-30 14:51:24.499 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.517 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-30 14:51:24.517 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.532 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-30 14:51:24.533 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.546 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-30 14:51:24.548 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.580 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-30 14:51:24.580 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.605 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-30 14:51:24.606 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.622 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-30 14:51:24.623 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.643 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-30 14:51:24.643 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.661 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-30 14:51:24.662 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.695 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-30 14:51:24.696 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.713 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-30 14:51:24.713 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.730 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-30 14:51:24.730 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.746 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-30 14:51:24.746 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.763 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-30 14:51:24.763 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.793 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-30 14:51:24.793 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.824 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-30 14:51:24.824 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.853 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-30 14:51:24.853 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.872 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-30 14:51:24.872 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:24.890 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-30 14:51:24.891 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 14:51:43.603 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:51:43.604 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:51:46.767 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:51:46.768 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:51:49.935 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:51:49.935 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:51:55.097 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:51:55.097 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:51:58.271 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:51:58.271 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:52:01.422 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:52:01.423 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:52:04.592 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:52:04.592 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:52:07.757 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:52:07.757 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:52:11.440 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:52:11.440 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:52:14.769 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 14:52:14.769 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 14:53:01.881 WARN  [RMI TCP Connection(7)-100.88.176.35] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor142.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-30 14:53:08.689 ERROR [main] c.a.d.f.s.StatFilter - slow sql 1857 millis. SELECT 1[]
2025-08-30 14:53:12.319 WARN  [RMI TCP Connection(7)-100.88.176.35] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 10302ms to respond
2025-08-30 14:56:12.994 ERROR [http-nio-28183-exec-1] c.h.c.c.e.GlobalExceptionHandler - /policeTagInfo/queryPersonTag --> 
java.lang.NullPointerException: null
	at com.hl.archive.service.PoliceTagInfoService.pagePolicePersonalTag(PoliceTagInfoService.java:246) ~[classes/:?]
	at com.hl.archive.service.PoliceTagInfoService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceTagInfoService$$EnhancerBySpringCGLIB$$1.pagePolicePersonalTag(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController.queryPersonTag(PoliceTagInfoController.java:110) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceTagInfoController$$EnhancerBySpringCGLIB$$1.queryPersonTag(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
2025-08-30 14:58:35.623 WARN  [http-nio-28183-exec-15] o.m.s.m.ClassPathMapperScanner - No MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
2025-08-30 14:58:35.859 ERROR [http-nio-28183-exec-15] c.h.c.c.e.GlobalExceptionHandler - /policeTagInfo/queryPersonTag --> 
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy199.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:119) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:84) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.sun.proxy.$Proxy263.pagePolicePersonalTag(Unknown Source) ~[?:?]
	at com.hl.archive.service.PoliceTagInfoService.pagePolicePersonalTag(PoliceTagInfoService.java:246) ~[classes/:?]
	at com.hl.archive.service.PoliceTagInfoService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceTagInfoService$$EnhancerBySpringCGLIB$$1.pagePolicePersonalTag(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController.queryPersonTag(PoliceTagInfoController.java:110) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceTagInfoController$$EnhancerBySpringCGLIB$$1.queryPersonTag(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:178) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.reflection.invoker.MethodInvoker.invoke(MethodInvoker.java:44) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:172) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
2025-08-30 14:58:58.073 ERROR [http-nio-28183-exec-4] c.h.c.c.e.GlobalExceptionHandler - /policeTagInfo/queryPersonTag --> 
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy199.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:119) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:84) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.sun.proxy.$Proxy263.pagePolicePersonalTag(Unknown Source) ~[?:?]
	at com.hl.archive.service.PoliceTagInfoService.pagePolicePersonalTag(PoliceTagInfoService.java:246) ~[classes/:?]
	at com.hl.archive.service.PoliceTagInfoService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceTagInfoService$$EnhancerBySpringCGLIB$$1.pagePolicePersonalTag(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController.queryPersonTag(PoliceTagInfoController.java:110) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceTagInfoController$$EnhancerBySpringCGLIB$$1.queryPersonTag(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:178) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.reflection.invoker.MethodInvoker.invoke(MethodInvoker.java:44) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:172) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
2025-08-30 14:58:58.653 ERROR [http-nio-28183-exec-21] c.h.c.c.e.GlobalExceptionHandler - /policeTagInfo/queryPersonTag --> 
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy199.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:119) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:84) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.sun.proxy.$Proxy263.pagePolicePersonalTag(Unknown Source) ~[?:?]
	at com.hl.archive.service.PoliceTagInfoService.pagePolicePersonalTag(PoliceTagInfoService.java:246) ~[classes/:?]
	at com.hl.archive.service.PoliceTagInfoService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceTagInfoService$$EnhancerBySpringCGLIB$$1.pagePolicePersonalTag(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController.queryPersonTag(PoliceTagInfoController.java:110) ~[classes/:?]
	at com.hl.archive.controller.PoliceTagInfoController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceTagInfoController$$EnhancerBySpringCGLIB$$1.queryPersonTag(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: org.apache.ibatis.reflection.ReflectionException: Could not set property 'tagNameList' of 'class com.hl.archive.domain.dto.PolicePersonalTagReturnDTO' with value '["星火计划"]' Cause: java.lang.IllegalArgumentException: argument type mismatch
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:178) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.reflection.invoker.MethodInvoker.invoke(MethodInvoker.java:44) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.setBeanProperty(BeanWrapper.java:172) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.set(BeanWrapper.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.reflection.MetaObject.setValue(MetaObject.java:138) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyPropertyMappings(DefaultResultSetHandler.java:512) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:418) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy408.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151) ~[pagehelper-5.3.3.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy407.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 138 more
2025-08-30 14:59:31.057 WARN  [Thread-26] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 14:59:31.057 WARN  [Thread-32] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 14:59:31.058 WARN  [Thread-32] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 14:59:31.061 WARN  [Thread-26] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 14:59:46.766 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-30 14:59:46.776 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-30 14:59:46.780 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-30 14:59:50.518 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 14:59:50.519 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:00:00.798 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-30 15:00:00.799 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.816 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-30 15:00:00.816 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.832 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-30 15:00:00.833 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.849 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-30 15:00:00.849 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.865 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-30 15:00:00.865 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.882 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-30 15:00:00.882 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.899 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-30 15:00:00.899 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.915 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-30 15:00:00.916 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.933 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-30 15:00:00.933 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.950 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-30 15:00:00.950 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.968 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-30 15:00:00.968 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:00.988 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-30 15:00:00.988 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.005 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-30 15:00:01.040 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-30 15:00:01.040 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.062 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-30 15:00:01.062 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.079 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-30 15:00:01.080 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.096 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-30 15:00:01.098 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.117 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-30 15:00:01.117 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.135 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-30 15:00:01.135 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.156 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-30 15:00:01.156 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.174 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-30 15:00:01.174 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.189 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-30 15:00:01.190 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.204 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-30 15:00:01.204 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.219 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-30 15:00:01.219 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.254 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-30 15:00:01.254 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.279 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-30 15:00:01.280 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.297 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-30 15:00:01.297 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.312 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-30 15:00:01.312 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.329 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-30 15:00:01.329 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.356 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-30 15:00:01.356 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.373 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-30 15:00:01.374 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.389 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-30 15:00:01.389 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.403 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-30 15:00:01.403 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.418 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-30 15:00:01.418 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.432 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-30 15:00:01.432 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.448 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-30 15:00:01.449 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.464 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-30 15:00:01.464 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.478 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-30 15:00:01.479 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.507 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-30 15:00:01.507 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.529 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-30 15:00:01.530 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.545 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-30 15:00:01.545 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.563 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-30 15:00:01.563 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.583 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-30 15:00:01.583 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.600 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-30 15:00:01.600 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.616 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-30 15:00:01.616 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.632 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-30 15:00:01.633 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.649 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-30 15:00:01.649 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.666 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-30 15:00:01.666 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.695 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-30 15:00:01.695 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.725 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-30 15:00:01.725 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.754 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-30 15:00:01.755 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.773 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-30 15:00:01.773 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:01.789 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-30 15:00:01.789 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:00:20.504 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:20.505 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:23.670 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:23.670 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:26.863 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:26.863 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:30.024 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:30.024 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:33.184 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:33.184 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:37.337 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:37.337 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:40.498 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:40.498 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:43.648 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:43.648 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:47.299 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:47.300 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:00:50.613 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:00:50.613 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:01:36.020 WARN  [RMI TCP Connection(7)-100.88.176.35] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor141.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-30 15:01:42.927 ERROR [main] c.a.d.f.s.StatFilter - slow sql 2024 millis. SELECT 1[]
2025-08-30 15:01:47.046 WARN  [RMI TCP Connection(7)-100.88.176.35] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 10905ms to respond
2025-08-30 15:05:01.268 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 15:05:01.268 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 15:05:01.268 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 15:05:01.277 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 15:36:43.817 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-30 15:36:43.826 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-30 15:36:43.830 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-30 15:36:47.730 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:47.731 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:36:58.111 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-30 15:36:58.111 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.128 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-30 15:36:58.129 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.146 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-30 15:36:58.146 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.163 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-30 15:36:58.163 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.179 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-30 15:36:58.179 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.196 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-30 15:36:58.196 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.213 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-30 15:36:58.213 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.229 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-30 15:36:58.229 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.245 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-30 15:36:58.246 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.262 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-30 15:36:58.262 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.282 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-30 15:36:58.282 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.300 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-30 15:36:58.301 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.334 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-30 15:36:58.369 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-30 15:36:58.369 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.392 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-30 15:36:58.392 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.409 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-30 15:36:58.409 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.434 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-30 15:36:58.434 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.454 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-30 15:36:58.455 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.477 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-30 15:36:58.477 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.499 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-30 15:36:58.499 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.517 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-30 15:36:58.517 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.533 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-30 15:36:58.533 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.547 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-30 15:36:58.547 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.562 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-30 15:36:58.562 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.600 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-30 15:36:58.600 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.625 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-30 15:36:58.625 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.641 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-30 15:36:58.642 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.657 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-30 15:36:58.657 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.673 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-30 15:36:58.674 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.689 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-30 15:36:58.689 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.706 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-30 15:36:58.706 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.721 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-30 15:36:58.721 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.735 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-30 15:36:58.735 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.749 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-30 15:36:58.749 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.765 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-30 15:36:58.765 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.782 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-30 15:36:58.782 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.799 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-30 15:36:58.800 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.812 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-30 15:36:58.813 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.840 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-30 15:36:58.841 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.862 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-30 15:36:58.862 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.877 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-30 15:36:58.877 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.895 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-30 15:36:58.895 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.914 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-30 15:36:58.915 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.935 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-30 15:36:58.935 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.950 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-30 15:36:58.951 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.968 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-30 15:36:58.968 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:58.986 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-30 15:36:58.986 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.005 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-30 15:36:59.005 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.033 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-30 15:36:59.033 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.066 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-30 15:36:59.066 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.112 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-30 15:36:59.112 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.129 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-30 15:36:59.129 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:36:59.146 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-30 15:36:59.146 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:37:17.134 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:17.135 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:20.279 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:20.279 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:23.458 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:23.458 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:26.638 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:26.638 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:29.790 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:29.790 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:32.955 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:32.956 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:36.100 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:36.100 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:39.221 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:39.221 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:42.898 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:42.898 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:37:46.220 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:37:46.220 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:38:33.054 WARN  [RMI TCP Connection(4)-100.88.176.35] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor142.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-30 15:38:39.431 ERROR [main] c.a.d.f.s.StatFilter - slow sql 2008 millis. SELECT 1[]
2025-08-30 15:41:43.031 WARN  [rebel-change-detector-thread] o.m.s.m.ClassPathMapperScanner - No MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
2025-08-30 15:44:20.114 ERROR [http-nio-28183-exec-1] c.h.c.c.e.GlobalExceptionHandler - 请求地址'/clubActivity/approveActivityTask',发生系统异常 -->> org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoSuchMethodError: com.hl.archive.domain.dto.PoliceClubActivityApproveTaskDTO.getCjrs()Ljava/lang/Integer;
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53)
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52)
Caused by: java.lang.NoSuchMethodError: com.hl.archive.domain.dto.PoliceClubActivityApproveTaskDTO.getCjrs()Ljava/lang/Integer;
	at com.hl.log.LogSlf4j.handler(LogSlf4j.java:130)
	at com.hl.log.LogSlf4j.doAfterReturning(LogSlf4j.java:56)
	at com.hl.archive.controller.PoliceClubActivityController$$EnhancerBySpringCGLIB$$1.approveActivityTask(<generated>)
2025-08-30 15:44:41.738 WARN  [http-nio-28183-exec-3] o.s.c.l.c.RoundRobinLoadBalancer - No servers available for service: hl-task
2025-08-30 15:44:41.744 WARN  [http-nio-28183-exec-3] o.s.c.o.l.RetryableFeignBlockingLoadBalancerClient - Service instance was not resolved, executing the original request
2025-08-30 15:44:44.472 ERROR [http-nio-28183-exec-3] c.h.c.c.e.GlobalExceptionHandler - 请求地址'/clubActivity/approveActivityTask',发生系统异常 -->> org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoSuchMethodError: com.hl.archive.domain.dto.PoliceClubActivityApproveTaskDTO.getCjrs()Ljava/lang/Integer;
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53)
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52)
Caused by: java.lang.NoSuchMethodError: com.hl.archive.domain.dto.PoliceClubActivityApproveTaskDTO.getCjrs()Ljava/lang/Integer;
	at com.hl.log.LogSlf4j.handler(LogSlf4j.java:130)
	at com.hl.log.LogSlf4j.doAfterThrowing(LogSlf4j.java:46)
	at com.hl.archive.controller.PoliceClubActivityController$$EnhancerBySpringCGLIB$$1.approveActivityTask(<generated>)
2025-08-30 15:45:16.011 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 15:45:16.011 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 15:45:16.011 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 15:45:16.020 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 15:45:32.767 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-30 15:45:32.775 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-30 15:45:32.780 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-30 15:45:36.562 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.562 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:36.564 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-30 15:45:46.722 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-30 15:45:46.722 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.752 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-30 15:45:46.753 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.770 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-30 15:45:46.770 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.787 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-30 15:45:46.787 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.802 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-30 15:45:46.803 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.819 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-30 15:45:46.819 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.836 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-30 15:45:46.837 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.851 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-30 15:45:46.852 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.867 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-30 15:45:46.867 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.884 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-30 15:45:46.884 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.901 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-30 15:45:46.901 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.919 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-30 15:45:46.919 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.937 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-30 15:45:46.971 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-30 15:45:46.971 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:46.992 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-30 15:45:46.992 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.009 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-30 15:45:47.009 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.025 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-30 15:45:47.025 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.045 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-30 15:45:47.045 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.065 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-30 15:45:47.065 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.087 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-30 15:45:47.087 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.106 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-30 15:45:47.106 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.120 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-30 15:45:47.121 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.136 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-30 15:45:47.136 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.150 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-30 15:45:47.151 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.186 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-30 15:45:47.186 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.211 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-30 15:45:47.211 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.226 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-30 15:45:47.227 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.241 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-30 15:45:47.241 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.258 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-30 15:45:47.258 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.274 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-30 15:45:47.274 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.291 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-30 15:45:47.291 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.305 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-30 15:45:47.305 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.319 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-30 15:45:47.319 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.334 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-30 15:45:47.334 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.348 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-30 15:45:47.348 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.364 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-30 15:45:47.365 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.381 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-30 15:45:47.381 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.395 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-30 15:45:47.395 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.423 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-30 15:45:47.423 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.444 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-30 15:45:47.444 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.459 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-30 15:45:47.459 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.477 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-30 15:45:47.477 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.495 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-30 15:45:47.495 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.512 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-30 15:45:47.512 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.528 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-30 15:45:47.528 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.543 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-30 15:45:47.543 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.559 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-30 15:45:47.560 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.576 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-30 15:45:47.576 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.603 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-30 15:45:47.604 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.634 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-30 15:45:47.634 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.663 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-30 15:45:47.663 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.681 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-30 15:45:47.681 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:45:47.698 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-30 15:45:47.698 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 15:46:06.797 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:06.798 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:09.949 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:09.949 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:14.135 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:14.135 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:17.310 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:17.310 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:20.494 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:20.495 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:23.669 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:23.669 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:26.843 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:26.843 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:30.004 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:30.004 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:33.707 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:33.709 WARN  [ForkJoinPool.commonPool-worker-2] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:46:37.036 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-30 15:46:37.037 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-30 15:47:25.999 WARN  [RMI TCP Connection(4)-100.88.176.35] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-30 15:47:32.599 ERROR [main] c.a.d.f.s.StatFilter - slow sql 1786 millis. SELECT 1[]
2025-08-30 15:47:36.309 WARN  [RMI TCP Connection(4)-100.88.176.35] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 10140ms to respond
2025-08-30 15:52:22.094 WARN  [http-nio-28183-exec-24] o.m.s.m.ClassPathMapperScanner - No MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
2025-08-30 15:52:23.262 WARN  [http-nio-28183-exec-24] o.s.c.l.c.RoundRobinLoadBalancer - No servers available for service: hl-task
2025-08-30 15:52:23.269 WARN  [http-nio-28183-exec-24] o.s.c.o.l.RetryableFeignBlockingLoadBalancerClient - Service instance was not resolved, executing the original request
2025-08-30 15:52:26.092 ERROR [http-nio-28183-exec-24] c.h.c.c.e.GlobalExceptionHandler - /clubActivity/approveActivityTask --> 
feign.RetryableException: hl-task executing POST http://hl-task/task/info/one
	at feign.FeignException.errorExecuting(FeignException.java:249) ~[feign-core-10.12.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:129) ~[feign-core-10.12.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89) ~[feign-core-10.12.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-10.12.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.5.jar:3.1.5]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.5.jar:3.1.5]
	at com.sun.proxy.$Proxy206.getOneTask(Unknown Source) ~[?:?]
	at com.hl.archive.service.PoliceClubActivityService.approveActivityTask(PoliceClubActivityService.java:250) ~[classes/:?]
	at com.hl.archive.service.PoliceClubActivityService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceClubActivityService$$EnhancerBySpringCGLIB$$1.approveActivityTask(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceClubActivityController.approveActivityTask(PoliceClubActivityController.java:72) ~[classes/:?]
	at com.hl.archive.controller.PoliceClubActivityController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceClubActivityController$$EnhancerBySpringCGLIB$$1.approveActivityTask(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.UnknownHostException: hl-task
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:184) ~[?:1.8.0_432-432]
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172) ~[?:1.8.0_432-432]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392) ~[?:1.8.0_432-432]
	at java.net.Socket.connect(Socket.java:607) ~[?:1.8.0_432-432]
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175) ~[?:1.8.0_432-432]
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:465) ~[?:1.8.0_432-432]
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:560) ~[?:1.8.0_432-432]
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:244) ~[?:1.8.0_432-432]
	at sun.net.www.http.HttpClient.New(HttpClient.java:341) ~[?:1.8.0_432-432]
	at sun.net.www.http.HttpClient.New(HttpClient.java:359) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1243) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1177) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1071) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:1005) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1357) ~[?:1.8.0_432-432]
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1332) ~[?:1.8.0_432-432]
	at feign.Client$Default.convertAndSend(Client.java:202) ~[feign-core-10.12.jar:?]
	at feign.Client$Default.execute(Client.java:103) ~[feign-core-10.12.jar:?]
	at org.springframework.cloud.openfeign.loadbalancer.LoadBalancerUtils.executeWithLoadBalancerLifecycleProcessing(LoadBalancerUtils.java:57) ~[spring-cloud-openfeign-core-3.1.5.jar:3.1.5]
	at org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient.lambda$execute$2(RetryableFeignBlockingLoadBalancerClient.java:168) ~[spring-cloud-openfeign-core-3.1.5.jar:3.1.5]
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:329) ~[spring-retry-1.3.4.jar:?]
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:225) ~[spring-retry-1.3.4.jar:?]
	at org.springframework.cloud.openfeign.loadbalancer.RetryableFeignBlockingLoadBalancerClient.execute(RetryableFeignBlockingLoadBalancerClient.java:114) ~[spring-cloud-openfeign-core-3.1.5.jar:3.1.5]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119) ~[feign-core-10.12.jar:?]
	... 139 more
2025-08-30 16:02:25.343 WARN  [http-nio-28183-exec-45] o.m.s.m.ClassPathMapperScanner - No MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
2025-08-30 16:03:25.566 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 16:03:25.566 WARN  [Thread-31] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 16:03:25.616 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 16:03:25.645 WARN  [Thread-25] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
